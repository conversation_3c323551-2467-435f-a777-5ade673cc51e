# 安装环境遇到问题:
1.清华源出问题，解决方法：直接终端设置如下命令。
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
    pip config set global.trusted-host mirrors.aliyun.com
    pip config set global.timeout 120
2.ninja多版本冲突.sudo apt install ninja-build全局安装+软连接+将冲突路径后移~/.bashrc

3.USB口权限问题
    永久：sudo usermod -a -G dialout $USER
    临时：sudo chmod 666 /dev/ttyUSB0



#编译+运行：
1.UART+USB0  esp32s3+不是2  齿轮中，xiaozhi文件选板。
2.登录管理页面 https://xiaozhi.me/devices  

#版本裁剪
一.修改WiFi名： 
    wifi_board.cc wifi_ap.SetSsidPrefix("Xiaozhi"); 修改WiFi名：wifi_board.cc wifi_ap.SetSsidPrefix("sexDoll"); 

; // 继承关系：class WifiBoard : public Board  1.尝试连接void WifiBoard::StartNetwork() { 2.开启热点 class WifiConfigurationAp
; // 详细步骤说明
; //     获取单例实例：
; //         获取Application和Board的单例实例。
; //         从Board中获取显示设备。
; //     尝试连接WiFi：
; //         获取WifiStation单例实例。
; //         设置显示状态为“正在连接”并启动WiFi连接。
; //         检查是否成功连接WiFi。
; //     启动WiFi配置AP：
; //         如果连接失败，设置设备状态为配置WiFi。
; //         启动WiFi配置AP，设置SSID前缀并启动AP。
; //         播报提示信息，显示配置WiFi的SSID和Web服务器URL。
; //     无限等待：
; //         进入无限等待循环，定期打印内存信息，直到设备重置。

二.OTA固件升级：
    服务端：
        1.CMakeLists.txt 下的set(PROJECT_VER "0.9.8") 改为 ： set(PROJECT_VER "1.9.8")  
        2.替换服务端文件：firmware_v1.9.8.bin
    客户端：
        1.修改齿轮中，xiaozhi文件。 OTA Version URL:http://*************:5000/version
; // OTA:
; // 原理：
; // Ota::CheckVersion() 访问一个URL，http://localhost:5000/version   原来：https://api.tenclass.net/xiaozhi/ota/
; // 同时携带Board::GetJson  得到的post_data_ 作为请求体。
; // 返回JSON，Response: { "firmware": { "version": "1.0.0", "url": "http://localhost:5000/firmware/1.1.0" } }
; // 如果版本号大于当前版本号，则请求版本URL。
; // xTaskCreate线程总入口：Application::CheckNewVersion() {} -> Ota::CheckVersion() {前三步} -> Ota::StartUpgrade -> Upgrade -> 

三.音频输入：采样 【不用调整，修改mqtt发布】
lichuang-dev ->config.h  设置 AUDIO_INPUT_SAMPLE_RATE  AUDIO_OUTPUT_SAMPLE_RATE  16000
; 音频数据流向：
;     硬件采集 -> AudioCodec::InputData(从编解码器获取音频数据) -> Application::InputAudio() -> AudioProcessor::Input() -> esp_afe_vc_v1.feed() 
;             -> AudioProcessor::AudioProcessorTask() -> output_callback_
;     硬件采集：通过ES7210等音频编解码器采集原始音频数据
;     AudioCodec::InputData()：
;         从硬件获取原始音频数据
;         支持单声道和双声道(带参考通道)模式
;     Application::InputAudio()：
;         处理采样率转换(如果需要转换到16kHz)
;         对于双通道数据，分离为麦克风通道和参考通道
;         调用audio_processor_.Input()进行音频处理
;     AudioProcessor::Input()：
;         将音频数据送入音频前端处理模块(esp_afe_vc_v1)
;         进行噪声抑制、自动增益控制等处理
;     AudioProcessor::AudioProcessorTask()：
;         在独立线程中运行
;         从音频处理模块获取处理后的数据
;         通过output_callback_将处理后的数据传递给回调函数
;     output_callback_的作用：
;         它是一个回调函数，用于接收经过处理后的音频数据
;         在AudioProcessor::OnOutput()中设置
;         当AudioProcessorTask获取到处理后的音频数据时会调用此回调
;         回调函数接收一个std::vector<int16_t>参数，包含处理后的音频样本
;         这个回调机制实现了音频处理模块和应用层的解耦，使得处理后的音频数据可以灵活地被使用

四.MqttProtocol类 mqtt_protocol.cc 

修改了 SendAudio 函数：
    ; 移除了 UDP 和加密相关代码
    ; 使用固定的 MQTT topic voice/assistant/2869365050091036673/data
    ; 直接通过 MQTT 发送音频数据
简化了 OpenAudioChannel 函数：
    ; 移除了 UDP 相关初始化代码
    ; 只保留 MQTT 连接检查
    ; 返回 true 表示音频通道已打开
简化了 CloseAudioChannel 函数：
    ; 移除了 UDP 清理代码
    ; 移除了发送 goodbye 消息
    ; 只保留回调函数调用

; 每次启动检查OTA服务器是否有新的配置？ ： 设备启动 -> 连接WiFi -> 访问OTA服务器 -> 获取MQTT配置 -> 保存到NVS -> MQTT客户端使用配置连接服务器
OTA服务器增加MQTT配置。mac转成十进制ID.
搜索端口号 8883 改为 1883
; MQTT_CONFIG = {
;     "endpoint": "14.116.172.207",  # MQTT服务器地址
;     "client_id": "esp32_client",     # 客户端ID前缀
;     "username": "tpkhytthubngt",        # MQTT用户名
;     "password": "Q2&gZr7*pXv!8Ny#",    # MQTT密码
;     "subscribe_topic": "voice/assistant/{device_id}/rx",  # 订阅主题模板
;     "publish_topic": "voice/assistant/{device_id}/tx"     # 发布主题模板
; }





app_main()
  ├── 初始化事件循环
  ├── 初始化NVS
  └── Application::Start()
      ├── 初始化显示
      ├── 初始化音频
      ├── 启动主循环
      ├── 连接网络
      └── 检查更新

InputAudio()
  ├── 采集音频数据
  ├── 重采样（如需）
  ├── 音频处理
  │   ├── ESP32-S3：唤醒词检测
  │   └── 其他：OPUS编码
  └── 发送数据

OutputAudio()
  ├── 接收音频数据
  ├── OPUS解码
  ├── 重采样（如需）
  └── 输出播放

MainLoop()
  ├── 等待事件
  │   ├── 音频输入就绪
  │   ├── 音频输出就绪
  │   └── 任务调度
  └── 处理对应事件

#3个线程
main_loop线程：优先级2，负责处理主要的事件循环
check_new_version线程：优先级1，负责检查固件更新
background_task线程：处理后台任务，如音频编解码等耗时操作。
#3个事件，在项目中的设置情况：
AUDIO_INPUT_READY_EVENT 在Application::Start()中设置：codec->OnInputReady
AUDIO_OUTPUT_READY_EVENT 在Application::Start()中设置：codec->OnOutputReady
MAIN_LOOP_EVENT 在Application::Schedule中设置：

1. 系统架构
核心组件
Application 类：整个应用的核心，采用单例模式
Board 类：硬件抽象层，管理各种硬件设备
Protocol 类：通信协议实现（MQTT/WebSocket）
AudioCodec 类：音频编解码处理

状态管理
设备状态机：包含启动、配网、空闲、连接、监听、说话等状态
状态转换由 SetDeviceState 函数管理
每个状态都有相应的UI反馈

音频处理流程
音频输入：采样 -> 重采样(如需) -> 编码(OPUS) -> 发送
音频输出：接收 -> 解码(OPUS) -> 重采样(如需) -> 播放
ESP32-S3 特有的语音唤醒功能

4. 特色功能
智能语音交互
    语音唤醒（ESP32-S3）
    语音识别
    语音合成播放

自动升级
    OTA固件升级
    版本检查
    自动重启

电源管理
    电池电量监控
    低功耗模式
    自动休眠




# managed_components 目录的作用
managed_components 目录通常用于存放项目中依赖的第三方组件或库。这些组件可能是通过包管理工具（如 CMake、vcpkg、conan 等）自动下载和管理的，或者是手动添加的预编译库。具体来说，managed_components 目录下的文件可能包括以下几类：

第三方库源码：

包含从外部仓库拉取的第三方库源代码，例如网络库、加密库等。
头文件：

第三方库的头文件，供项目中的其他模块引用。
预编译库文件：

预编译的静态库或动态库文件（如 .a, .lib, .so, .dll），用于链接到项目中。
配置文件：

用于描述组件依赖关系、版本信息、构建选项等的配置文件（如 component.json, CMakeLists.txt 等）。
示例代码：

提供使用这些组件的示例代码或测试用例，帮助开发者快速上手。
许可证文件：

包含第三方库的许可证信息，确保项目符合开源协议的要求。


