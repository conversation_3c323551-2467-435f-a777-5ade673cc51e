/**
 * @file application.cc
 * @brief ESP32S3 AiBox应用程序核心实现
 *
 * 本文件实现了AiBox设备的核心应用逻辑，包括：
 * - 设备状态管理和状态机控制
 * - 音频处理流水线（采集→处理→编码→传输）
 * - 多线程任务调度和事件处理
 * - 网络通信协议管理
 * - IoT设备集成和控制
 * - 电源管理和省电机制
 *
 * @note 本文件包含多个编译条件开关，用于支持不同硬件配置和功能特性
 * @warning 音频处理部分涉及实时性要求，修改时需注意线程安全和时序
 *
 * <AUTHOR> AiBox Team
 * @date 2024
 */

#include "application.h"

// 系统和网络相关头文件
#include <arpa/inet.h>
#include <cJSON.h>
#include <driver/gpio.h>
#include <esp_log.h>
#include "esp_timer.h"
#include <cmath>
#include <cstring>
#include <random>

// 硬件抽象层和驱动
#include "audio_codec.h"
#include "board.h"
#include "boards/common/button.h"
#include "display.h"
#include "esp32_s3_szp.h"
#include "esp_sleep.h"
#include "font_awesome_symbols.h"

// 应用层模块
#include "iot/thing_manager.h"
#include "led/single_led.h"
#include "ml307_ssl_transport.h"
#include "mqtt_protocol.h"
#include "system_info.h"
#include "websocket_protocol.h"
#include "../local_moaning_data.h"

// 板级特定配置
#include "boards/lichuang-dev/uart_433.h"
#include "project_config.h"
#include "esp_wifi.h"
#include "Local_moan.h"

#define TAG "Application"

// 条件编译：IMU参数配置相关
#if FF_IMU_PARAM_IS_Wire == 0
static int last_button_int = 0;   ///< 上次按键中断状态，用于按键防抖
static bool first_key = true;     ///< 首次按键标志，用于初始化处理
#endif

// 条件编译：本地音频播放功能
#if LOCAL_MOAN_PLAYING == 1
static uint8_t moan_count = 1;           ///< 本地音频播放计数器
static bool local_moan_is_playing = false;  ///< 本地音频播放状态标志
static bool IMU_CHANGE_FLAG = false;     ///< IMU状态变化标志，用于触发音频播放
#endif

// 条件编译：WiFi信号检查音频提示
#if WIFI_SIGNAL_CHECK_TONE == 1
static bool mqtt_first_connect_flag = false;  ///< MQTT首次连接标志，用于播放连接成功提示音
#endif

// 条件编译：433MHz MAC地址发送功能
#if SEND_MAC_ADDRESS_433 == 1
bool OTA_UPGRADE_FLAG = false;  ///< OTA升级标志，用于控制升级流程
#endif

// ============================================================================
// 嵌入式音频资源声明
// 这些音频文件在编译时被嵌入到固件中，用于系统提示音
// ============================================================================

#if WIFI_SIGNAL_CHECK_TONE == 1
/// WiFi信号弱提示音频数据（OPUS格式）
extern const char p3_wifi_weak_start[] asm("_binary_wifi_weak_p3_start");
extern const char p3_wifi_weak_end[]   asm("_binary_wifi_weak_p3_end");
#endif

/// 网络断开提示音频数据（OPUS格式）
#if WIFI_CONNECT_CHECK_TONE == 1
/// 网络断开提示音频数据（OPUS格式）
extern const char p3_NETWORKdisc_start[] asm("_binary_NETWORKdisc_p3_start");
extern const char p3_NETWORKdisc_end[]   asm("_binary_NETWORKdisc_p3_end");
#endif

// 多语言WiFi配置提示音频
/// 中文WiFi配置提示音频（OPUS格式）
extern const char p3_zh_start[] asm("_binary_linkwifi_zh_p3_start");
extern const char p3_zh_end[] asm("_binary_linkwifi_zh_p3_end");

/// 英文WiFi配置提示音频（OPUS格式）
extern const char p3_en_start[] asm("_binary_linkwifi_en_p3_start");
extern const char p3_en_end[] asm("_binary_linkwifi_en_p3_end");

/// 日文WiFi配置提示音频（OPUS格式）
extern const char p3_ja_start[] asm("_binary_linkwifi_ja_p3_start");
extern const char p3_ja_end[] asm("_binary_linkwifi_ja_p3_end");

// 系统提示音频
/// "叮"声提示音频，用于语音唤醒确认（OPUS格式）
extern const char p3_dong_start[] asm("_binary_dong_p3_start");
extern const char p3_dong_end[] asm("_binary_dong_p3_end");

/// 系统更新提示音频（OPUS格式）
extern const char p3_update_start[] asm("_binary_update_p3_start");
extern const char p3_update_end[] asm("_binary_update_p3_end");

// 错误提示音频资源宏定义
MoaningString(err_pin);  ///< 引脚错误提示音频
MoaningString(err_reg);  ///< 寄存器错误提示音频

/**
 * @brief 设备状态字符串映射表
 *
 * 用于日志输出和调试，将设备状态枚举值转换为可读的字符串
 * 数组索引对应DeviceState枚举值
 */
static const char* const STATE_STRINGS[] = {
    "unknown",      ///< 未知状态
    "starting",     ///< 启动中
    "configuring",  ///< WiFi配置中
    "idle",         ///< 空闲状态
    "connecting",   ///< 连接中
    "listening",    ///< 监听中（等待语音输入）
    "speaking",     ///< 播放中（输出音频）
    "upgrading",    ///< 升级中
    "moaning",      ///< 本地音频播放中
    "offline",      ///< 离线状态
    "invalid_state" ///< 无效状态（数组边界保护）
};


/**
 * @brief Application类构造函数
 *
 * 初始化应用程序的核心组件和默认参数：
 * - 创建FreeRTOS事件组用于任务同步
 * - 初始化后台任务处理器（32KB堆栈）
 * - 配置OTA升级参数
 * - 设置音频处理默认参数
 * - 预分配音频缓冲区内存
 *
 * @note 构造函数中不进行硬件初始化，硬件相关操作在Start()方法中进行
 * @warning 后台任务使用动态内存分配，需要在析构函数中释放
 */
Application::Application() {
  // 创建FreeRTOS事件组，用于线程间事件同步
  event_group_ = xEventGroupCreate();

  // 创建后台任务处理器，堆栈大小32KB，用于处理OPUS编解码等耗时操作
  background_task_ = new BackgroundTask(4096 * 8);

  // 初始化OTA（Over-The-Air）升级相关参数
  ota_.SetCheckVersionUrl(CONFIG_OTA_VERSION_URL);  // 设置版本检查URL
  ota_.SetHeader("Device-Id", SystemInfo::GetMacAddress().c_str());  // 设置设备ID为MAC地址

  // 设置OPUS解码器采样率为16kHz，匹配服务器发送的PCM数据采样率
  opus_decode_sample_rate_ = 16000;

  // 预分配音频缓冲区内存，避免运行时动态分配造成的延迟
  for (auto& buffer : audio_buffers_) {
    buffer.reserve(4096);  // 每个缓冲区预分配4KB空间
  }

  // 初始化音频输出时间戳，用于音频同步和延迟计算
  last_output_time_ = std::chrono::steady_clock::now();

  // 设置默认音量为80%，符合用户体验最佳实践
  current_volume_ = 80;
}

/**
 * @brief Application类析构函数
 *
 * 清理应用程序占用的资源：
 * - 释放后台任务处理器内存
 * - 删除FreeRTOS事件组
 *
 * @note 析构函数会在系统关闭或重启时调用
 * @warning 确保所有线程已经停止后再调用析构函数
 */
Application::~Application() {
  // 释放后台任务处理器内存
  if (background_task_ != nullptr) {
    delete background_task_;
  }

  // 删除FreeRTOS事件组，释放系统资源
  vEventGroupDelete(event_group_);
}

/**
 * @brief 检查并执行固件版本升级
 *
 * 该函数运行在独立线程中，定期检查服务器是否有新的固件版本。
 * 如果发现新版本，会自动进行OTA升级流程：
 * 1. 等待设备进入空闲状态
 * 2. 停止所有音频处理
 * 3. 显示升级进度
 * 4. 执行固件下载和安装
 *
 * @note 该函数包含无限循环，应在独立线程中运行
 * @warning 升级过程中会停止所有音频功能，升级成功后设备会自动重启
 * @thread_safety 线程安全，使用Schedule()方法确保主线程执行升级操作
 */
void Application::CheckNewVersion() {
  auto& board = Board::GetInstance();
  auto display = board.GetDisplay();

  // 设置OTA请求的POST数据，包含设备信息用于服务器验证
  ota_.SetPostData(board.GetJson());

  // 版本检查主循环，持续监控新版本
  while (true) {
    // 向服务器查询是否有新版本可用
    if (ota_.CheckVersion()) {
      if (ota_.HasNewVersion()) {

        #if SEND_MAC_ADDRESS_433 == 1
        // 设置OTA升级标志，用于433MHz通信模块
        OTA_UPGRADE_FLAG = true;
        #endif

        #if BOOT_BEGAIN_OTA == 0
        // 等待设备进入空闲状态再开始升级，避免中断正在进行的对话
        do {
          vTaskDelay(pdMS_TO_TICKS(3000));  // 每3秒检查一次设备状态
        } while (GetDeviceState() != kDeviceStateIdle);
        #endif

        // 使用主任务执行升级，确保升级过程不可被取消
        Schedule([this, &board, display]() {
          // 切换到升级状态，此状态下不允许其他状态转换
          SetDeviceState(kDeviceStateUpgrading);

          // 更新显示界面，显示升级图标和版本信息
          display->SetIcon(FONT_AWESOME_DOWNLOAD);
          display->SetStatus("新版本 " + ota_.GetFirmwareVersion());

          auto codec = board.GetAudioCodec();
          codec->EnableOutput(true);

          // 播放升级提示音，告知用户升级开始
          PlayOpusFile(p3_update_start, p3_update_end - p3_update_start);
          ESP_LOGE(TAG, "Firmware upgrade...");
          vTaskDelay(pdMS_TO_TICKS(2500));  // 等待提示音播放完成

          // === 升级前的资源清理阶段 ===

          // 关闭麦克风输入，避免升级过程中的音频干扰
          codec->EnableInput(false);

          // 清空音频解码队列，释放缓存的音频数据
          {
            audio_decode_queue_->clear();
          }

          // 等待所有后台任务完成，确保没有正在进行的编解码操作
          background_task_->WaitForCompletion();

          // 停止音频处理器和语音唤醒检测（仅ESP32S3平台）
#if CONFIG_IDF_TARGET_ESP32S3
          audio_processor_.Stop();
          wake_word_detect_.StopDetection();
#endif

          // 额外等待1秒，确保所有音频操作完全停止
          vTaskDelay(pdMS_TO_TICKS(1000));

          // === 开始OTA升级流程 ===

          // 启动升级，传入进度回调函数
          ota_.StartUpgrade([this, display](int progress, size_t speed) {
            // 格式化升级进度信息：百分比 + 下载速度
            char buffer[64];
            snprintf(buffer, sizeof(buffer), "%d%% %zuKB/s", progress, speed / 1024);

            // 如果音频输出完成，播放升级进度提示音
            if (oudio_output_finish_) {
              PlayOpusFile(p3_update_start, p3_update_end - p3_update_start);
              oudio_output_finish_ = false;
            }
            // 可选：更新显示状态（当前被注释）
            // display->SetStatus(buffer);
          });

          // 注意：如果升级成功，设备会自动重启，不会执行到这里
          // 如果执行到这里，说明升级失败
          ESP_LOGE(TAG, "Firmware upgrade failed...");
          // 升级失败处理
          display->SetStatus("升级失败");
          vTaskDelay(pdMS_TO_TICKS(3000));  // 显示失败信息3秒

          // 升级失败后重启设备，尝试恢复到之前的固件版本
          esp_restart();
        });
      } else {
        // 当前版本已是最新，标记为有效版本
        ota_.MarkCurrentVersionValid();
        // 可选：显示当前版本信息（当前被注释）
        // display->ShowNotification("版本 " + ota_.GetCurrentVersion());
      }
      return;  // 检查完成，退出函数
    }

    // 如果网络连接失败或服务器无响应，60秒后重新检查
    vTaskDelay(pdMS_TO_TICKS(60000));
  }
}

/**
 * @brief 系统警告和提示音播放
 *
 * 根据不同的警告类型播放相应的提示音，支持多语言提示。
 * 主要用于WiFi配置和注册失败等场景的用户提示。
 *
 * @param title 警告标题
 * @param message 警告消息内容
 *
 * @note 当前实现固定播放英文提示音，多语言支持代码被注释
 * @thread_safety 线程安全，可在任意线程中调用
 */
void Application::Alert(const std::string& title, const std::string& message) {
  ESP_LOGW(TAG, "Alert: %s, %s", title.c_str(), message.c_str());

  // 检查是否为需要播放提示音的特定消息
  if (message == "Configuring WiFi" || message == "Registration denied") {
    // 从NVS（非易失性存储）获取用户设置的语言偏好
    std::string language = GetLanguageFromNVS();
    ESP_LOGI(TAG, "Current language setting: %s", language.c_str());

    // 当前固定播放英文WiFi配置提示音
    // TODO: 未来可根据language变量选择对应语言的音频文件
    PlayOpusFile(p3_en_start, p3_en_end - p3_en_start);

    // 多语言支持代码（当前被注释，保留用于未来扩展）
    // 支持的语言包括：中文(zh)、英文(en)、日文(ja)、韩文(ko)、
    // 德文(de)、法文(fr)、西班牙文(es)、葡萄牙文(pt)
    /*
    根据语言选择对应的音频文件
    if (language == "zh") {
      PlayOpusFile(p3_zh_start, p3_zh_end - p3_zh_start);
    } else if (language == "en") {
      PlayOpusFile(p3_en_start, p3_en_end - p3_en_start);
    } else if (language == "ja") {
      PlayOpusFile(p3_ja_start, p3_ja_end - p3_ja_start);
    } else {
      // 默认使用英语，如果没有找到匹配的语言
      ESP_LOGW(TAG, "Unknown language: %s, using English as default", language.c_str());
      PlayOpusFile(p3_en_start, p3_en_end - p3_en_start);
    }
    */
  }
}

/**
 * @brief 播放嵌入式OPUS音频文件
 *
 * 播放编译时嵌入到固件中的OPUS格式音频文件，主要用于系统提示音。
 * 音频数据通过音频解码队列进行异步播放，不会阻塞调用线程。
 *
 * @param data 指向OPUS音频数据的指针（通常来自嵌入式资源）
 * @param size 音频数据的字节大小
 *
 * @note 该函数是异步的，调用后立即返回，音频在后台播放
 * @note 支持的格式：OPUS编码的音频数据（可能包含Ogg容器）
 * @thread_safety 线程安全，内部使用音频解码队列管理并发
 *
 * @see audio_decode_queue_->PlayLocalFile() 实际的音频播放实现
 */
void Application::PlayOpusFile(const char* data, size_t size) {
  // 将音频数据加入解码队列进行异步播放
  audio_decode_queue_->PlayLocalFile(data, size);

  // 调试日志（当前被注释）
  // ESP_LOGW(TAG, "PlayOpusFile: %zu bytes", size);

  /*
   * 以下是Ogg容器格式检测和解析的代码（当前被注释）
   * 保留用于未来可能的Ogg格式支持
   *
   * Ogg是OPUS音频的标准容器格式，包含元数据和音频包
   * 检测方法：文件头部包含'OggS'魔数
   */

  //   // 检查是否为Ogg封装格式 (头部是'OggS')
  //   if (size >= 4 && data[0] == 'O' && data[1] == 'g' && data[2] == 'g' &&
  //       data[3] == 'S') {
  //     ESP_LOGI(TAG, "Detected Ogg container format, extracting Opus packets...");

  //     // 为了简单，我们将Ogg文件视为一系列数据包
  //     // 实际上，这需要更复杂的Ogg解析
  //     const char* p = data;
  //     bool found_opus_header = false;

  //     // 搜索"OpusHead"标记，确认为Opus数据
  //     for (size_t i = 0; i < size - 8; i++) {
  //       if (memcmp(data + i, "OpusHead", 8) == 0) {
  //         found_opus_header = true;
  //         ESP_LOGI(TAG, "Found OpusHead marker at offset %d", i);
  //         break;
  //       }
  //     }

  //     if (!found_opus_header) {
  //       ESP_LOGE(TAG, "Ogg file does not contain Opus data");
  //       return;
  //     }

  //     // 设置为播放模式
  //     SetDeviceState(kDeviceStateSpeaking);
  //     SetDecodeSampleRate(16000);

  //     // 直接将整个Ogg文件发送到解码器
  //     // 这依赖于解码器能够处理Ogg容器
  //     std::vector<uint8_t> opus(data, data + size);
  //     // std::lock_guard<std::mutex> lock(mutex_);
  //     audio_decode_queue_->push_back(std::move(opus));

  //     // 确保音频输出设备已启用
  //     auto codec = Board::GetInstance().GetAudioCodec();
  //     codec->EnableOutput(true);

  //     // 触发音频输出事件
  //     xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
  //     return;
  //   }

  //   // 如果不是Ogg格式，尝试使用BinaryProtocol3格式解析
  //   ESP_LOGI(TAG, "Trying to parse as BinaryProtocol3 format...");
  //   PlayLocalFile(data, size);
}


#if WIFI_CONNECT_CHECK_TONE == 1
/**
 * @brief 播放网络断开提示音
 *
 * 当检测到网络连接断开时播放提示音，告知用户网络状态变化。
 * 该功能仅在启用WiFi连接检查音频提示时可用。
 *
 * @note 仅在编译时定义WIFI_CONNECT_CHECK_TONE=1时可用
 * @thread_safety 线程安全，可在任意线程中调用
 */
void Application::PlayNetworkDisc() {
    // 播放嵌入式网络断开提示音频（OPUS格式）
    PlayOpusFile(reinterpret_cast<const char*>(p3_NETWORKdisc_start),
                 p3_NETWORKdisc_end - p3_NETWORKdisc_start);
}
#endif
// void Application::PlayLocalFile(const char* data, size_t size) {
//   ESP_LOGI(TAG, "PlayLocalFile: %zu bytes", size);
//   SetDecodeSampleRate(16000);
//   for (const char* p = data; p < data + size;) {
//     auto p3 = (BinaryProtocol3*)p;
//     p += sizeof(BinaryProtocol3);

//     auto payload_size = ntohs(p3->payload_size);
//     std::vector<uint8_t> opus;
//     opus.resize(payload_size);
//     memcpy(opus.data(), p3->payload, payload_size);
//     p += payload_size;

//     // std::lock_guard<std::mutex> lock(mutex_);
//     audio_decode_queue_->emplace_back(std::move(opus));
//   }
// }

/**
 * @brief 切换聊天状态
 *
 * 根据当前设备状态智能切换到下一个合适的聊天状态。
 * 该函数实现了用户交互的核心逻辑，支持按键触发的状态切换。
 *
 * 状态切换逻辑：
 * 1. 空闲状态 -> 连接状态 -> 监听状态（开始语音对话）
 * 2. 播放状态 -> 中止播放（用户打断AI回复）
 * 3. 监听状态 -> 关闭音频通道（停止监听）
 *
 * @note 该函数通过Schedule()调度到主线程执行，确保状态切换的原子性
 * @thread_safety 线程安全，内部使用Schedule()确保主线程执行
 * @see SetDeviceState() 设备状态管理
 * @see protocol_->OpenAudioChannel() 音频通道管理
 */
void Application::ToggleChatState() {
  // 调度到主线程执行，确保状态切换的线程安全性
  Schedule([this]() {
    // 检查网络协议是否已初始化
    if (!protocol_) {
      ESP_LOGE(TAG, "Protocol not initialized");
      return;
    }

    if (device_state_ == kDeviceStateIdle) {
      // === 从空闲状态激活语音对话功能 ===

      // 首先切换到连接状态，准备建立音频通道
      SetDeviceState(kDeviceStateConnecting);

      // 尝试打开与服务器的音频通道
      if (!protocol_->OpenAudioChannel()) {
        // 音频通道打开失败，显示错误提示并回到空闲状态
        Alert("Error", "Failed to open audio channel");
        SetDeviceState(kDeviceStateIdle);
        return;
      }

      // 设置持续监听标志，保持语音监听状态
      keep_listening_ = true;

      // 切换到监听状态，开始接收用户语音输入
      SetDeviceState(kDeviceStateListening);

    } else if (device_state_ == kDeviceStateSpeaking) {
      // === 用户打断AI播放 ===
      // 如果AI正在播放回复，用户可以通过按键中止播放
      // 中止当前的AI语音播放，允许用户重新输入
      AbortSpeaking(kAbortReasonNone);

    } else if (device_state_ == kDeviceStateListening) {
      // === 停止语音监听 ===
      // 如果正在监听用户语音，则关闭音频通道停止监听
      protocol_->CloseAudioChannel();
    }
  });
}

/**
 * @brief 开始语音监听
 *
 * 激活设备的语音监听功能，准备接收用户的语音输入。
 * 该函数处理从不同状态到监听状态的转换，确保音频通道正确建立。
 *
 * 处理逻辑：
 * 1. 检查网络协议是否已初始化
 * 2. 空闲状态：打开音频通道并切换到监听状态
 * 3. 播放状态：中止当前播放，等待缓冲区清空后开始监听
 *
 * @note 该函数设置keep_listening_=false，表示单次监听模式
 * @note 与ToggleChatState()不同，该函数专门用于启动监听
 * @thread_safety 线程安全，通过Schedule()在主线程执行
 * @see ToggleChatState() 状态切换函数
 * @see StopListening() 停止监听函数
 */
void Application::StartListening() {
  // 调度到主线程执行，确保状态切换的原子性
  Schedule([this]() {
    // 检查网络协议是否已初始化
    if (!protocol_) {
      ESP_LOGE(TAG, "Protocol not initialized");
      return;
    }

    // 设置为单次监听模式（非持续监听）
    keep_listening_ = false;

    if (device_state_ == kDeviceStateIdle) {
      // === 从空闲状态开始监听 ===

      // 检查音频通道是否已经打开
      if (!protocol_->IsAudioChannelOpened()) {
        // 音频通道未打开，需要先建立连接
        SetDeviceState(kDeviceStateConnecting);

        if (!protocol_->OpenAudioChannel()) {
          // 音频通道打开失败，显示错误并回到空闲状态
          SetDeviceState(kDeviceStateIdle);
          Alert("Error", "Failed to open audio channel");
          return;
        }
      }

      // 切换到监听状态，开始接收语音输入
      SetDeviceState(kDeviceStateListening);

    } else if (device_state_ == kDeviceStateSpeaking) {
      // === 从播放状态切换到监听状态 ===

      // 先中止当前的音频播放
      AbortSpeaking(kAbortReasonNone);

      // FIXME: 等待扬声器清空音频缓冲区
      // 这个延时确保播放完全停止，避免音频干扰
      vTaskDelay(pdMS_TO_TICKS(120));

      // 切换到监听状态
      SetDeviceState(kDeviceStateListening);
    }
  });
}

/// QMI8658 IMU传感器数据结构体全局变量
/// 用于存储6轴惯性测量单元（加速度计+陀螺仪）的传感器数据
/// 支持运动检测、姿态感知等功能
t_sQMI8658 QMI8658;

/**
 * @brief 停止监听
 * 将设备从监听状态切换到空闲状态
 * 主要操作：
 * 1. 发送停止监听命令到服务器
 * 2. 将设备状态设置为空闲状态
 */
void Application::StopListening() {
  Schedule([this]() {
    if (device_state_ == kDeviceStateListening) {
      // 向服务器发送停止监听命令
      protocol_->SendStopListening();
      ESP_LOGI(TAG, "StopListeningo ");
      // 切换到空闲状态
      SetDeviceState(kDeviceStateIdle);
    }
  });
}

/**
 * @brief 启动应用程序主要功能模块
 *
 * 这是应用程序的核心初始化函数，负责启动所有主要功能模块：
 * 1. 初始化硬件组件（IMU、音频编解码器）
 * 2. 配置音频处理流水线（编码器、重采样器）
 * 3. 设置音频中断回调函数
 * 4. 创建多个工作线程处理不同任务
 * 5. 启动网络通信和IoT功能
 *
 * @note 该函数在main.cc中被调用，是整个应用的入口点
 * @warning 该函数会创建多个FreeRTOS任务，确保有足够的堆栈空间
 * @thread_safety 该函数应该只在主线程中调用一次
 */
void Application::Start() {
  // 获取硬件抽象层实例
  auto& board = Board::GetInstance();

  // === 硬件初始化阶段 ===

  // 初始化QMI8658 IMU传感器，用于检测设备姿态和运动
  qmi8658_init(board.GetI2cDevice());

  // 设置设备状态为启动中
  SetDeviceState(kDeviceStateStarting);

  // 可选：获取IMU的初始倾角数据（当前被注释）
  // qmi8658_fetch_angleFromAcc(&QMI8658);

  // === 显示器初始化（当前被注释） ===
  // auto display = board.GetDisplay();

  // === 音频编解码器初始化 ===

  auto codec = board.GetAudioCodec();
  ESP_LOGI(TAG, "Codec configuration: input_rate=%d, output_rate=%d",
           codec->input_sample_rate(), codec->output_sample_rate());

  // 从NVS加载用户设置的音量，如果没有则使用默认值80%
  int default_volume = 80;
  current_volume_ = LoadVolumeFromNVS(default_volume);
  codec->SetOutputVolume(current_volume_);
  ESP_LOGI(TAG, "Set initial volume to %d", current_volume_);

  // === 音频编码器初始化 ===

  // 创建OPUS编码器：16kHz采样率，单声道，20ms帧长度
  // 16kHz是语音应用的标准采样率，平衡了音质和数据量
  opus_encoder_ = std::make_unique<OpusEncoderWrapper>(16000, 1, OPUS_FRAME_DURATION_MS);

  // 如果音频编解码器的输入采样率不是16kHz，需要配置重采样器
  if (codec->input_sample_rate() != 16000) {
    input_resampler_.Configure(codec->input_sample_rate(), 16000);      // 主音频重采样
    reference_resampler_.Configure(codec->input_sample_rate(), 16000);  // 参考信号重采样（用于AEC）
  }

  // === 音频中断回调配置 ===

  // 配置音频输入就绪中断回调
  // 当音频编解码器有新的输入数据时，触发AUDIO_INPUT_READY_EVENT事件
  codec->OnInputReady([this, codec]() {
    BaseType_t higher_priority_task_woken = pdFALSE;
    xEventGroupSetBitsFromISR(event_group_, AUDIO_INPUT_READY_EVENT,
                              &higher_priority_task_woken);
    return higher_priority_task_woken == pdTRUE;  // 返回是否需要进行上下文切换
  });

  // 配置音频输出就绪中断回调（当前功能被注释）
  codec->OnOutputReady([this]() {
    BaseType_t higher_priority_task_woken = pdFALSE;
    // 音频输出事件处理（当前被注释，保留用于未来扩展）
    // xEventGroupSetBitsFromISR(event_group_, AUDIO_OUTPUT_READY_EVENT,
    //                           &higher_priority_task_woken);
    return higher_priority_task_woken == pdTRUE;
  });

  // 启动音频编解码器，开始音频数据流处理
  codec->Start();


  // === 音频解码队列初始化 ===

  #if 1
  // 当前使用的音频解码队列配置（简化版本）
  audio_decode_queue_ = std::make_unique<AudioDecodeQueue>(
      board.GetAudioCodec(),
      [this]() -> bool {
        // 音频播放完成回调：设置输出完成标志
        oudio_output_finish_ = true;
        return false;  // 返回false表示不需要特殊处理
      });

  #else
  // 备用的音频解码队列配置（包含更复杂的播放后处理逻辑）
  audio_decode_queue_ = std::make_unique<AudioDecodeQueue>(
      board.GetAudioCodec(),
      [this]() -> bool {
        // 设置音频输出完成标志
        oudio_output_finish_ = true;
        ESP_LOGI("[output]", "**************** output02 value: %d*****************", oudio_output_finish_);

        // 如果设置了播放后停止标志，则切换到监听状态
        if (stop_after_playback_) {
          stop_after_playback_ = false;
          Schedule([this]() {
            SetDeviceState(kDeviceStateListening);  // 切换到监听状态
            voice_detected_ = true;                  // 标记检测到语音
            silence_count_ = 10;                     // 重置静音计数
          });
          return true;  // 返回true表示需要特殊处理
        }
        return false;
      });
  #endif

  // === 多线程任务创建 ===

  /**
   * 主循环线程：处理事件调度和状态管理
   * - 任务名称: "main_loop"
   * - 堆栈大小: 4KB
   * - 优先级: 2（中等优先级）
   * - 功能: 处理Schedule()调度的任务和设备状态转换
   */
  xTaskCreate(
      [](void* arg) {
        Application* app = (Application*)arg;
        app->MainLoop();
        vTaskDelete(NULL);  // 任务结束时删除自身
      },
      "main_loop", 4096, this, 2, nullptr);

  /**
   * IMU读取线程：处理惯性测量单元数据
   * - 任务名称: "imu_read"
   * - 堆栈大小: 4024字节
   * - 优先级: 2（中等优先级）
   * - 功能: 读取IMU传感器数据，检测设备运动状态
   */
  xTaskCreate(
      [](void* arg) {
        Application* app = (Application*)arg;
        app->ImuRead();
        vTaskDelete(NULL);
      },
      "imu_read", 4024, this, 2, nullptr);

  /**
   * 音频输入处理线程：处理实时音频输入
   * - 任务名称: "input_audio"
   * - 堆栈大小: 8KB（较大，因为涉及音频处理）
   * - 优先级: 5（最高优先级，确保音频实时性）
   * - 功能: 处理麦克风输入、语音检测、音频编码
   */
  xTaskCreate(
      [](void* arg) {
        Application* app = (Application*)arg;
        app->InputAudioLoop();
        vTaskDelete(NULL);
      },
      "input_audio", 8192, this, 5, nullptr);

  #if 1
  /**
   * 音频监控线程：监控音频队列和停止条件
   * - 任务名称: "monitor_stop_audio"
   * - 堆栈大小: 4KB
   * - 优先级: 1（最低优先级，后台监控任务）
   * - 功能: 监控音频播放状态、处理停止条件、电池管理
   */
  xTaskCreate(
    [](void* arg) {
      Application* app = (Application*)arg;
      app->MonitorStopAndAudioQueue();
    },
    "monitor_stop_audio", 4096, this, 1, nullptr);
  #endif

  // === 条件编译：WiFi信号监控线程 ===

  #if WIFI_SIGNAL_CHECK_TONE == 1
  /**
   * WiFi信号强度监控线程（条件编译）
   * - 任务名称: "monitor_wifi_rssi"
   * - 堆栈大小: 4KB
   * - 优先级: 1（低优先级后台任务）
   * - 功能: 监控WiFi信号强度，信号弱时播放提示音
   */
  xTaskCreate(
    [](void* arg) {
        Application* app = (Application*)arg;
        app->MonitorWifiRssi();
        vTaskDelete(NULL);
    },
    "monitor_wifi_rssi", 4096, this, 1, nullptr);
  #endif

  // === 网络初始化和版本检查 ===

  // 启动网络连接（WiFi或4G），等待网络就绪
  board.StartNetwork();

  /**
   * 固件版本检查线程
   * - 任务名称: "check_new_version"
   * - 堆栈大小: 6.8KB（较大，因为涉及HTTP请求和OTA操作）
   * - 优先级: 1（低优先级后台任务）
   * - 功能: 定期检查服务器是否有新固件版本，自动执行OTA升级
   */
  xTaskCreate(
      [](void* arg) {
        Application* app = (Application*)arg;
        app->CheckNewVersion();
        vTaskDelete(NULL);
      },
      "check_new_version", 6800, this, 1, nullptr);

  // === 网络自愈机制（条件编译选择不同实现） ===

  #if 0
  // 备用的自愈任务实现（当前被禁用）
  // 该实现包含更全面的状态检查和自动修复逻辑
  xTaskCreate(
    [](void* arg) {
        Application* app = (Application*)arg;
        while (true) {
            vTaskDelay(pdMS_TO_TICKS(3000));  // 每3秒检查一次

            // 只在MQTT已连接时进行自愈检查
            if (app->protocol_ && app->protocol_->IsMqttConnected()) {
                app->Schedule([app]() {
                    // 自愈逻辑1：监听状态但音频通道未打开
                    if (app->device_state_ == kDeviceStateListening && !app->protocol_->IsAudioChannelOpened()) {
                        ESP_LOGI("[application]", "Timing self-healing: the listening state automatically opens the audio channel.");
                        app->protocol_->OpenAudioChannel();
                    }
                    // 自愈逻辑2：播放状态但音频队列为空
                    if (app->device_state_ == kDeviceStateSpeaking && app->audio_decode_queue_ && app->audio_decode_queue_->empty()) {
                        ESP_LOGI("[application]", "Timing self-healing: speaking but the audio queue is empty, and it will automatically switch back to listening.");
                        app->SetDeviceState(kDeviceStateListening);
                        app->voice_detected_ = true;
                        app->silence_count_ = 10;
                    }
                });
            }
        }
    },
    "self_heal_task", 1024, this, 1, nullptr);

  #else
  /**
   * MQTT连接自愈线程（当前使用的实现）
   * - 任务名称: "mqtt_self_heal"
   * - 堆栈大小: 4KB
   * - 优先级: 1（低优先级后台任务）
   * - 功能: 监控MQTT连接状态，断线时自动重连
   * - 重连策略: 最多连续重试5次，失败后等待6秒再重试
   */
  xTaskCreate(
      [](void* arg) {
          Application* app = (Application*)arg;
          int8_t retry_count = 0;  // 重试计数器

          while (true) {
              vTaskDelay(pdMS_TO_TICKS(1000));  // 每秒检查一次

              // 检查WiFi连接状态
              wifi_ap_record_t ap_info;
              bool wifi_connected = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);

              // 如果设备在监听状态、MQTT断开但WiFi连接正常，尝试重连MQTT
              if (app->GetDeviceState() == kDeviceStateListening &&
                  app->protocol_ &&
                  (!app->protocol_->IsMqttConnected()) &&
                  wifi_connected) {

                  ESP_LOGW("MQTT", "MQTT discon., try to redect... ------>002");

                  if (retry_count < 5) {  // 最多连续重试5次，防止死循环
                      app->protocol_->StartMqttClient();
                      retry_count++;
                  } else {
                      ESP_LOGE("MQTT", "MQTTreconnection failed too many times,waiting for network recovery ");
                      vTaskDelay(pdMS_TO_TICKS(6000));  // 失败后延长等待时间
                      retry_count = 0;  // 重置计数器
                  }
              } else if (app->protocol_ && app->protocol_->IsMqttConnected()) {
                  retry_count = 0;  // MQTT连接成功后重置重试计数器
              }
          }
      },
      "mqtt_self_heal", 4096, this, 1, nullptr);
  #endif



  // === 音频处理配置宏定义 ===
  #define up_audio 0  ///< 音频上传功能开关（当前禁用）

  // === ESP32S3平台特有的音频处理初始化 ===
  #if CONFIG_IDF_TARGET_ESP32S3

  /**
   * 音频处理器初始化
   * - 配置输入通道数和参考信号
   * - 启用AEC（回声消除）和NS（噪声抑制）功能
   */
  audio_processor_.Initialize(codec->input_channels(), codec->input_reference());

  /**
   * 音频处理输出回调：实现完整的音频处理流水线
   * 流程：音频处理器输出 → 后台任务 → OPUS编码 → 主任务 → 网络发送
   *
   * @note 使用多层回调确保线程安全和实时性：
   *       1. 后台任务处理耗时的OPUS编码
   *       2. 主任务处理网络发送（避免网络阻塞音频处理）
   */
  audio_processor_.OnOutput([this](std::vector<int16_t>&& data) {
    // 将OPUS编码任务调度到后台线程，避免阻塞音频处理
    background_task_->Schedule([this, data = std::move(data)]() mutable {
        // 执行OPUS编码（耗时操作）
        opus_encoder_->Encode(std::move(data),
                              [this](std::vector<uint8_t>&& opus) {
                                // 将网络发送任务调度到主线程
                                Schedule([this, opus = std::move(opus)]() {
                                  protocol_->SendAudio(opus);
                                });
                              });
    });
  });

  /**
   * 语音唤醒检测器初始化
   * - 配置输入通道数和参考信号
   * - 基于ESP-SR库实现本地语音唤醒
   */
  wake_word_detect_.Initialize(codec->input_channels(), codec->input_reference());

  /**
   * VAD（Voice Activity Detection）状态变化回调
   *
   * 该回调处理语音活动检测的核心逻辑：
   * - 检测用户是否在说话
   * - 管理静音超时和状态转换
   * - 控制音频通道的开启和关闭
   *
   * @param speaking true表示检测到语音活动，false表示静音
   * @note 该回调在音频处理线程中调用，使用Schedule()确保线程安全
   */
  wake_word_detect_.OnVadStateChange([this](bool speaking) {
    Schedule([this, speaking]() {
      // 计算距离上次语音活动的时间间隔
      auto now = std::chrono::steady_clock::now();
      int duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                         now - last_voice_time_).count();

      // === 播放状态下的语音检测处理 ===
      if (device_state_ == kDeviceStateSpeaking) {
        voice_detected_ = true;  // 播放状态下标记检测到语音
      }

      // === 监听状态下的语音活动检测和超时管理 ===
      if (device_state_ == kDeviceStateListening) {

        // 备用的TTS取消逻辑（当前被注释）
        // 该逻辑用于在检测到用户说话时立即取消正在播放的TTS
        /*
        if (speaking && !state_sepeaking_ && !cancel_tts_sent_) {
          // 从非说话状态变为说话状态时发送cancelTTS，并且当前listening周期内未发送过
          protocol_->SendCancelTTS();
          ESP_LOGI(TAG, "Voice detected, sending CancelTTS");
          cancel_tts_sent_ = true; // 标记已在本次listening周期内发送过CancelTTS
        }
        */

        // === 语音活动检测处理 ===
        if (speaking) {
          // 唤醒阶段结束后的防抖处理
          if (wake_stage_end_) {
            if (++wake_stage_end_cnt_ < 6) {
              return;  // 小于6帧（100-200ms）时直接返回，避免误触发
            }
            wake_stage_end_cnt_ = 0;
            wake_stage_end_ = false;
          }

          // 检测到语音活动时的状态更新
          voice_detected_ = true;
          silence_count_ = 0;           // 重置静音计数
          audio_channel_close_ = false; // 标记音频通道为开启状态

        } else if (voice_detected_) {
          // === 静音检测和超时处理 ===
          wake_stage_end_cnt_ = 0;

          // 只有当静音持续时间超过200ms时才处理（防抖）
          if (duration >= 200) {
            silence_count_++;
            last_voice_time_ = std::chrono::steady_clock::now();

            #if SILENCE_COUNT_DEBUG == 1
            ESP_LOGW("SilenceVAD", "...............Silence count: %d, duration: %dms..................", silence_count_, duration);
            #endif

            // 静音超时处理：800ms后关闭音频通道
            if (silence_count_ == 4) {  // 4 * 200ms = 800ms
              ESP_LOGI(TAG, "Silence exceeds 800ms after voice detected, closing audio channel");
              protocol_->CloseAudioChannel();
              audio_channel_close_ = true;
              wake_stage_end_ = false;
            }
            // 长时间静音处理：返回空闲状态
            else if (silence_count_ == idle_timeout_duration_ * 60) {
              ESP_LOGI(TAG, "Silence exceeds %dms after voice detected, setting to idle",
                       idle_timeout_duration_ * 60);
              SetDeviceState(kDeviceStateIdle);
              PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);  // 播放结束提示音
              voice_detected_ = false;
              wake_stage_end_ = true;
              silence_count_ = 0;
            }
          }
        }
      } else if (device_state_ == kDeviceStateSpeaking) {
        // === 播放状态下的语音检测处理 ===
        voice_detected_ = true;

        // 备用的播放状态下语音检测逻辑（当前被注释）
        // 该逻辑用于在播放状态下检测到用户说话时的处理
        if (state_sepeaking_ == false && speaking) {
          // 可选的处理方案：中断当前播放，切换到监听状态
          // AbortSpeaking(kAbortReasonWakeWordDetected);
          // protocol_->SendCancelTTS();
          // SetDeviceState(kDeviceStateListening);
        }
      }

      // 更新说话状态标志，用于下次状态变化检测
      state_sepeaking_ = speaking;
    });
  });

  /**
   * 语音唤醒词检测回调：处理唤醒词被检测到的事件
   *
   * 这是整个语音交互流程的起始点，当检测到预设的唤醒词时触发。
   * 主要处理流程：
   * 1. 播放确认提示音
   * 2. 根据当前设备状态执行相应的状态转换
   * 3. 重置音频处理器和编码器状态
   * 4. 建立与服务器的音频通道连接
   *
   * @param wake_word 检测到的唤醒词字符串
   * @note 该回调在音频处理线程中调用，使用Schedule()确保线程安全
   * @thread_safety 通过Schedule()调度到主线程执行，确保状态操作的原子性
   */
  wake_word_detect_.OnWakeWordDetected([this](const std::string& wake_word) {
    Schedule([this, wake_word]() {
      ESP_LOGW(TAG, " >>>> Wake word detected: %s <<<<", wake_word.c_str());

      // === 唤醒确认处理 ===

      // 清空当前音频播放队列，避免与提示音冲突
      audio_decode_queue_->clear();

      // 播放"叮"声确认唤醒成功，给用户即时反馈
      PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);

      // 等待提示音播放完成，避免与后续音频处理冲突
      vTaskDelay(pdMS_TO_TICKS(650));

      // 确保音频处理器处于运行状态，准备接收用户语音
      audio_processor_.Start();

      // === 根据当前设备状态执行相应的处理逻辑 ===

      if (device_state_ == kDeviceStateIdle) {
        // === 空闲状态 → 监听状态 ===

        // 重置音频解码器状态，清除之前的解码缓存
        ResetDecoder();

        // 重置OPUS编码器状态，确保编码质量
        opus_encoder_->ResetState();

        // 尝试与服务器建立音频通道连接
        if (!protocol_->OpenAudioChannel()) {
          ESP_LOGE(TAG, "Failed to open audio channel");
          SetDeviceState(kDeviceStateIdle);  // 连接失败，保持空闲状态
          wake_word_detect_.StartDetection(); // 重新开始唤醒词检测
          return;
        }

        // 设置持续监听标志
        keep_listening_ = true;

        // 发送取消TTS指令，确保服务器停止任何正在进行的语音合成
        protocol_->SendCancelTTS();

        // 切换到监听状态，开始接收用户语音输入
        SetDeviceState(kDeviceStateListening);

      } else if (device_state_ == kDeviceStateSpeaking) {
        // === 播放状态 → 监听状态 ===

        // 中止当前的语音播放，原因是检测到唤醒词
        AbortSpeaking(kAbortReasonWakeWordDetected);

        // 发送取消TTS指令，停止服务器端的语音合成
        protocol_->SendCancelTTS();

        // 重置MQTT停止标志
        mqtt_stop_flag_ = false;

        // 切换到监听状态，准备接收新的用户指令
        SetDeviceState(kDeviceStateListening);

      } else if (device_state_ == kDeviceStateListening) {
        // === 监听状态 → 重新开始监听 ===

        // 虽然已经在监听状态，但发送取消TTS确保服务器状态同步
        protocol_->SendCancelTTS();
      }

      // === 唤醒后的状态重置和初始化 ===

      // 重新启动唤醒词检测，准备下次唤醒
      wake_word_detect_.StartDetection();

      // 更新最后语音活动时间戳，用于静音超时计算
      last_voice_time_ = std::chrono::steady_clock::now();

      // 设置静音计数为11，给用户足够的反应时间
      silence_count_ = 11;

      // 标记唤醒阶段结束，进入正常语音检测模式
      wake_stage_end_ = true;

      // 标记检测到语音活动
      voice_detected_ = true;
    });
  });

  // 启动语音唤醒词检测，开始监听用户的唤醒指令
  wake_word_detect_.StartDetection();
  #endif

  // === 网络通信协议初始化 ===

  /**
   * 初始化MQTT通信协议
   *
   * MQTT协议用于与服务器进行双向通信：
   * - 上行：发送音频数据、设备状态、IoT控制指令
   * - 下行：接收音频数据、控制指令、配置更新
   */

  // 可选：更新显示状态（当前被注释）
  // display->SetStatus("初始化协议");

  // 创建MQTT协议实例，使用智能指针管理内存
  protocol_ = std::make_unique<MqttProtocol>();

  // 启动MQTT客户端连接
  // 采用异步启动方式，不阻塞主线程，连接失败时由自愈机制处理
  protocol_->StartMqttClient();

  // 备用的同步连接方式（当前被注释）
  // 该方式会阻塞等待连接成功，适用于必须确保连接的场景
  /*
  while (!protocol_->StartMqttClient()) {
    SetDeviceState(kDeviceStateConnecting);  // 设置为连接中状态
    vTaskDelay(pdMS_TO_TICKS(1000));         // 等待1秒后重试
    ESP_LOGW(TAG, "wifi maby not connect....");
  }
  */

  // === 网络协议事件回调配置 ===

  #if WIFI_SIGNAL_CHECK_TONE == 1
  /**
   * MQTT连接成功回调（条件编译）
   *
   * 当MQTT客户端成功连接到服务器时触发，主要用于：
   * - 首次连接标记管理
   * - 连接恢复后的状态自愈
   * - 音频通道状态检查和修复
   */
  auto mqtt = protocol_.get();
  if (mqtt) {
      mqtt->OnConnected([this]() {

          if (!mqtt_first_connect_flag) {
            // 首次连接，仅设置标志，不执行自愈逻辑
            mqtt_first_connect_flag = true;
          } else {
            // 重连成功，执行状态自愈逻辑
            Schedule([this]() {
                // 自愈逻辑1：监听状态但音频通道未打开时自动打开
                if (device_state_ == kDeviceStateListening && !protocol_->IsAudioChannelOpened()) {
                    ESP_LOGI("[application780]", "listen state and audio channel not opened, auto open audio channel");
                    protocol_->OpenAudioChannel();
                }

                // 自愈逻辑2：播放状态但音频队列为空时切换到监听状态
                if (device_state_ == kDeviceStateSpeaking && audio_decode_queue_ && audio_decode_queue_->empty()) {
                    ESP_LOGI("[application785]", "speaking state but audio queue is empty, auto switch to listening");
                    SetDeviceState(kDeviceStateListening);
                    voice_detected_ = true;
                    silence_count_ = 10;  // 给用户足够的反应时间
                }
            });
          }
      });
  }
  #endif

  /**
   * 网络错误回调：处理网络连接异常
   *
   * 当网络连接出现问题时触发，执行以下操作：
   * - 播放错误提示音
   * - 根据配置切换到离线模式
   *
   * @param message 错误消息描述
   * @note 网络错误可能由WiFi断开、服务器不可达、MQTT连接失败等原因引起
   */
  protocol_->OnNetworkError([this](const std::string& message) {
    // 播放错误提示音，告知用户网络异常
    Alert("Error", std::move(message));

    #if LOCAL_MOAN_PLAYING == 1
    // 如果启用了本地音频播放功能，切换到离线模式
    ESP_LOGW(TAG, "The network is disconnected and enters offline mode: %s", message.c_str());
    Schedule([this]() { SetDeviceState(kDeviceStateOffline); });
    #endif
  });
  /**
   * 音频数据接收回调：处理服务器发送的音频数据
   *
   * 当服务器通过网络发送音频数据时触发，主要用于：
   * - 接收TTS（文本转语音）生成的音频数据
   * - 更新音频接收时间戳
   * - 将音频数据加入播放队列
   *
   * @param data 服务器发送的音频数据（OPUS格式）
   * @note 只有在播放状态下才会处理音频数据，避免状态混乱
   * @thread_safety 回调在网络线程中执行，数据移动语义确保线程安全
   */
  protocol_->OnIncomingAudio([this](std::vector<uint8_t>&& data) {
    // 更新最后音频接收时间，用于超时检测和状态管理
    last_audio_received_time_ = std::chrono::steady_clock::now();

    // 只有在播放状态下才处理接收到的音频数据
    if (device_state_ == kDeviceStateSpeaking) {
      start_play_voice_num_ = 0;  // 重置播放开始计数器
      // 将音频数据加入解码播放队列，使用移动语义避免数据拷贝
      audio_decode_queue_->push_back(std::move(data));
    }
  });

  /**
   * 音频通道打开回调：处理音频通道建立成功事件
   *
   * 当与服务器的音频通道成功建立时触发，执行以下初始化操作：
   * - 禁用设备省电模式，确保音频处理性能
   * - 检查并配置音频采样率
   * - 发送IoT设备描述符到服务器
   * - 处理离线模式恢复
   * - 执行状态自愈检查
   *
   * @note 该回调标志着设备可以开始双向音频通信
   * @thread_safety 回调在网络线程中执行，使用Schedule()确保状态操作的线程安全
   */
  protocol_->OnAudioChannelOpened([this, codec, &board]() {
    // === 性能优化配置 ===

    // 禁用省电模式，确保音频处理的实时性和稳定性
    board.SetPowerSaveMode(false);

    // === 音频采样率配置检查 ===

    // 检查服务器音频采样率与设备输出采样率是否匹配
    if (protocol_->server_sample_rate() != codec->output_sample_rate()) {
      ESP_LOGW(TAG,
               "服务器的音频采样率 %d 与设备输出的采样率 %d "
               "不一致，重采样后可能会失真",
               protocol_->server_sample_rate(), codec->output_sample_rate());
    }

    // 设置解码器采样率以匹配服务器
    SetDecodeSampleRate(protocol_->server_sample_rate());

    // === IoT设备集成 ===

    // 清空上次的IoT状态缓存
    last_iot_states_.clear();

    // 获取IoT设备管理器并发送设备描述符到服务器
    // 这使得服务器能够了解设备支持的IoT功能（如灯光控制、传感器等）
    auto& thing_manager = iot::ThingManager::GetInstance();
    protocol_->SendIotDescriptors(thing_manager.GetDescriptorsJson());

    // === 离线模式恢复处理 ===

    #if LOCAL_MOAN_PLAYING == 1
    // 如果设备之前处于离线状态，网络恢复后切换回正常状态
    if (device_state_ == kDeviceStateOffline) {
        ESP_LOGI(TAG, "网络恢复，退出offline模式");
        SetDeviceState(kDeviceStateIdle);  // 切换到空闲状态
    }
    #endif

    // === 状态自愈检查 ===

    // 调度状态检查任务到主线程，确保设备状态的一致性
    Schedule([this]() {
        if (device_state_ == kDeviceStateListening) {
          // 监听状态下不需要特殊处理
        } else if (device_state_ == kDeviceStateSpeaking) {
          // 播放状态下检查音频队列，如果为空则切换到监听状态
          if (audio_decode_queue_ && audio_decode_queue_->empty()) {
              SetDeviceState(kDeviceStateListening);
              voice_detected_ = true;
              silence_count_ = 10;  // 给用户足够的反应时间
          }
        }
    });



  });
  /**
   * @brief 音频通道关闭回调函数
   *
   * 当服务器主动关闭音频通道时触发，通常发生在以下场景：
   * - 用户语音输入结束，服务器处理完成
   * - 网络连接异常导致通道断开
   * - 服务器端超时或错误处理
   *
   * 主要功能：
   * 1. 启用省电模式，降低设备功耗
   * 2. 清理音频通道相关状态
   * 3. 记录通道关闭事件用于调试
   *
   * @note 该回调不会自动切换设备状态，状态管理由其他逻辑处理
   * @thread_safety 回调在网络线程中执行，使用Schedule()确保状态操作的线程安全
   */
  protocol_->OnAudioChannelClosed([this, &board]() {
    // === 省电模式配置 ===
    // 音频通道关闭后启用省电模式，降低CPU频率和功耗
    board.SetPowerSaveMode(true);

    // 调度清理任务到主线程执行
    Schedule([this]() {
      // 可选：清除显示界面的聊天消息（当前被注释）
      // auto display = Board::GetInstance().GetDisplay();
      // display->SetChatMessage("", "");

      // 记录音频通道关闭事件，用于调试和状态跟踪
      ESP_LOGW(TAG, "OnAudioChannelClosed");

      // 可选的状态切换逻辑（当前被注释）
      // SetDeviceState(kDeviceStateIdle);      // 切换到空闲状态
      // SetDeviceState(kDeviceStateListening); // 或者保持监听状态
    });
  });
  
  /**
   * @brief JSON消息处理回调函数
   *
   * 处理从服务器接收到的JSON格式控制消息，这是设备与服务器进行高级交互的核心机制。
   * 该回调支持多种消息类型，实现了完整的设备控制和状态管理功能。
   *
   * 支持的消息类型：
   * 1. 语言设置消息 (languagesType) - 设置设备的语言偏好
   * 2. 呼吟控制消息 (moan) - 控制本地音频播放功能
   * 3. 欠费/离线控制消息 (arrears) - 处理账户状态和离线模式
   * 4. TTS消息 (tts) - 文本转语音播放控制
   * 5. 设备控制消息 (device) - 音量调节、关机等设备操作
   * 6. IoT控制消息 (iot) - 智能家居设备控制
   *
   * @param root 指向cJSON根对象的指针，包含服务器发送的完整消息
   * @note 该回调在网络线程中执行，复杂操作使用Schedule()调度到主线程
   * @thread_safety 使用Schedule()确保状态修改操作的线程安全性
   * @see cJSON 第三方JSON解析库，用于解析服务器消息
   */
  protocol_->OnIncomingJson([this](const cJSON* root) {
    // === JSON消息有效性检查 ===
    if (!root) {
      ESP_LOGW(TAG, "Received null JSON root");
      return;
    }

    // === 语言设置消息处理 ===
    // 检查是否包含语言设置指令
    cJSON* languagesType = cJSON_GetObjectItem(root, "languagesType");
    if (languagesType && cJSON_IsString(languagesType)) {
      // 处理语言设置消息，更新设备的语言偏好
      // 提取语言设置值并记录日志
      std::string language = languagesType->valuestring;
      ESP_LOGI(TAG, "Received language setting: %s", language.c_str());

      // 将语言设置持久化保存到NVS（非易失性存储）
      SaveLanguageTypeToNVS(language);

      // 更新网络协议中的语言设置，影响后续的语音识别和TTS
      protocol_->UpdateLanguage(language);

      return;  // 语言设置消息处理完成，直接返回
    }

    // === 本地音频播放控制消息处理 ===
    // 检查是否包含本地音频播放控制指令
    cJSON* control_moan_type = cJSON_GetObjectItem(root, "control_moan");
    if (control_moan_type) {
      ESP_LOGI(TAG, "Received control_moan message: %s", control_moan_type->valuestring);
      const char* type_str = control_moan_type->valuestring;

      // 根据控制指令设置本地音频播放状态
      if (strcmp(type_str, "0") == 0) {
        // === 关闭本地音频播放功能 ===
        moaning_flag_ = false;  // 全局播放标志
        ESP_LOGI(TAG, "Setting moaning_state to OFF");
        moaning_state_.first = false;      // 播放状态标志
        moaning_state_.second.clear();     // 清空自定义音频标识

      } else if (strcmp(type_str, "1") == 0) {
        // === 开启本地音频播放功能（默认模式） ===
        moaning_flag_ = true;
        ESP_LOGI(TAG, "Setting moaning_state to ON with empty string");
        moaning_state_.first = true;       // 启用播放状态
        moaning_state_.second = "";        // 使用默认音频

      } else {
        // === 开启本地音频播放功能（自定义模式） ===
        moaning_flag_ = true;
        ESP_LOGI(TAG, "Setting moaning_state to ON with custom value: %s", type_str);
        moaning_state_.first = true;       // 启用播放状态
        moaning_state_.second = std::string(control_moan_type->valuestring);  // 自定义音频标识
      }
      return;  // 本地音频控制消息处理完成
    }

#if LOCAL_MOAN_PLAYING == 1
    // === 账户状态和离线模式控制消息处理（条件编译） ===

    /**
     * 欠费控制消息处理
     * 当用户账户欠费或需要强制离线时，服务器发送此消息
     * 设备收到后将切换到离线模式，停止云端服务但保持本地功能
     */
    cJSON* arrears = cJSON_GetObjectItem(root, "arrears");
    if (arrears && cJSON_IsTrue(arrears)) {
        ESP_LOGW(TAG, "收到欠费/单机命令，进入offline模式");
        // 调度状态切换到主线程，确保状态转换的原子性
        Schedule([this]() { SetDeviceState(kDeviceStateOffline); });
        return;  // 欠费消息处理完成
    }

    /**
     * 离线模式控制消息处理
     * 服务器可以主动要求设备进入离线模式
     * 通常用于维护、升级或特殊情况下的服务暂停
     */
    cJSON* offline = cJSON_GetObjectItem(root, "offline");
    if (offline && cJSON_IsTrue(offline)) {
        ESP_LOGW(TAG, "收到offline命令，进入offline模式");
        // 调度状态切换到主线程，确保状态转换的原子性
        Schedule([this]() { SetDeviceState(kDeviceStateOffline); });
        return;  // 离线消息处理完成
    }
#endif

    // === 通用消息类型检查 ===
    // 检查消息是否包含有效的type字段，用于确定消息类型
    cJSON* type = cJSON_GetObjectItem(root, "type");
    if (!type || !cJSON_IsString(type)) {
      ESP_LOGW(TAG, "Missing or invalid type field");
      return;
    }

    const char* type_str = type->valuestring;
    ESP_LOGI(TAG, "Processing JSON message with type: %s", type_str);

    // === TTS（文本转语音）消息处理 ===
    if (strcmp(type_str, "tts") == 0) {
      /**
       * TTS消息用于控制文本转语音的播放流程
       * 支持的状态：start（开始播放）、stop（停止播放）、sentence_start（句子开始）
       */
      auto state = cJSON_GetObjectItem(root, "state");
      if (!state || !cJSON_IsString(state)) {
        ESP_LOGW(TAG, "Invalid TTS state");
        return;
      }

      // 根据TTS状态执行相应的播放控制操作
      if (strcmp(state->valuestring, "start") == 0) {
        // === TTS播放开始处理 ===

        // 切换设备状态到播放模式
        SetDeviceState(kDeviceStateSpeaking);

        // 设置播放控制标志
        start_play_voice_ = true;      // 标记开始播放语音
        start_play_voice_num_ = 0;     // 重置播放计数器

        // 清空音频解码队列，确保新的TTS音频能够立即播放
        // std::lock_guard<std::mutex> lock(mutex_);  // 可选的线程安全保护
        audio_decode_queue_->clear();

        // 记录播放开始时间，用于播放时长统计和超时检测
        playback_start_time_ = std::chrono::steady_clock::now();

        // 记录音频配置信息，用于调试和问题排查
        ESP_LOGI(TAG, "Starting playback with sample rate: %d Hz",
                 opus_decode_sample_rate_);
        auto codec = Board::GetInstance().GetAudioCodec();
        if (!codec) {
          ESP_LOGE(TAG, "Codec is null");
          return;
        }
        ESP_LOGI(TAG, "Codec output sample rate: %d Hz",
                 codec->output_sample_rate());

        // 启用音频输出硬件，开始播放音频
        codec->EnableOutput(true);
      } else if (strcmp(state->valuestring, "stop") == 0) {
        // === TTS播放停止处理 ===

        // 刷新音频段缓冲区，确保当前播放的音频段完整播放完毕
        audio_decode_queue_->FlushSegmentBuffer();

        #if 1
        // 当前使用的停止处理方式：调度到主线程执行
        Schedule([this]() {
           // 清除播放控制标志
           start_play_voice_ = false;      // 停止播放语音标志
           start_play_voice_num_ = 0;      // 重置播放计数器

           // 如果当前处于播放状态，设置MQTT停止标志
           // 该标志用于通知监控线程执行状态转换
           if (device_state_ == kDeviceStateSpeaking) {
             mqtt_stop_flag_ = true;       // 设置停止标志，触发状态转换
           }
           // 调试日志（当前被注释）
           //ESP_LOGI("[MQTT_JSON]", "__stop flag_set_mqtt_stop_flag_ = %d", mqtt_stop_flag_.load());
        });
        #else
        // 备用的停止处理方式：直接在当前线程执行（当前被禁用）
        start_play_voice_num_ = 0;
        start_play_voice_ = false;
        // 调试日志（当前被注释）
        //ESP_LOGI("[MQTT_JSON]", "start_play_voice_num_ = %d, start_play_voice_ = %d",start_play_voice_num_, start_play_voice_);
        #endif

      } else if (strcmp(state->valuestring, "sentence_start") == 0) {
        // === TTS句子开始播放事件处理 ===

        // 该事件在TTS开始播放新句子时触发，可用于显示文本内容
        // 当前实现被注释，保留用于未来的显示功能扩展

        // 可选的文本显示功能（当前被注释）
        // auto text = cJSON_GetObjectItem(root, "text");
        // if (text && cJSON_IsString(text) && display) {
        //     ESP_LOGI(TAG, "<< %s", text->valuestring);
        //     display->SetChatMessage("assistant", text->valuestring);
        // }
      }
    } else if (strcmp(type_str, "0") == 0 || strcmp(type_str, "1") == 0 ||
               strcmp(type_str, "2") == 0 || strcmp(type_str, "3") == 0) {
      // === 设备控制消息处理 ===
      /**
       * 设备控制消息用于远程控制设备的各种功能
       * 控制类型：
       * - type "0": 音量控制
       * - type "1": 关机控制
       * - type "2": 空闲超时设置
       * - type "3": 其他控制功能
       */

      // 检查控制值字段是否存在
      cJSON* vlue = cJSON_GetObjectItem(root, "vlue");
      if (!vlue || !cJSON_IsString(vlue)) {
        ESP_LOGW(TAG, "Missing or invalid vlue field in control message");
        return;
      }

      // 解析控制消息参数
      int type_val = atoi(type_str);                    // 控制类型（0-3）
      std::string control_value = vlue->valuestring;    // 控制值

      ESP_LOGI(TAG, "Processing control message: type=%d, value=%s", type_val,
               control_value.c_str());

      // === 音量控制处理 (type=0) ===
      if (type_val == 0) {
        auto codec = Board::GetInstance().GetAudioCodec();
        if (!codec) {
          ESP_LOGE(TAG, "Codec is null");
          return;
        }

        int new_volume = current_volume_;

        // 根据控制值执行不同的音量调节操作
        if (control_value == "+") {
          // 音量增加一个步长（通常为10%）
          new_volume = current_volume_ + kVolumeStep;
        } else if (control_value == "-") {
          // 音量减少一个步长
          new_volume = current_volume_ - kVolumeStep;
        } else if (control_value == "++") {
          // 音量设置为最大值
          new_volume = kVolumeMax;
        } else if (control_value == "--") {
          // 音量设置为最小值
          new_volume = kVolumeMin;
        } else {
          // 直接设置具体的音量数值
          try {
            new_volume = std::stoi(control_value);
          } catch (const std::exception& e) {
            ESP_LOGW(TAG, "Invalid volume value: %s", control_value.c_str());
            return;
          }
        }

        // 限制音量范围在有效区间内
        new_volume = std::max(kVolumeMin, std::min(100, new_volume));

        // 应用新的音量设置
        ESP_LOGW(TAG, "Setting volume: %d -> %d", current_volume_, new_volume);
        codec->SetOutputVolume(new_volume);    // 设置硬件音量
        current_volume_ = new_volume;          // 更新当前音量变量
        SaveVolumeToNVS(new_volume);          // 持久化保存音量设置
      } else if (type_val == 1) {
        // === 关机控制处理 (type=1) ===
        ESP_LOGW(TAG, "Shutdown requested");
        auto& board = Board::GetInstance();

        // 关闭设备电源管理
        SetPowerOffset(false);

        // 等待1秒确保所有操作完成
        vTaskDelay(pdMS_TO_TICKS(1000));

        // 进入ESP32深度睡眠模式，实现软关机
        esp_deep_sleep_start();

      } else if (type_val == 2) {
        // === 空闲超时设置 (type=2) ===
        ESP_LOGW(TAG, "Setting idle timeout duration: %s",
                 control_value.c_str());

        /**
         * 空闲超时时间设置，单位：分钟
         * 当设备在监听状态下超过设定时间无语音活动时，
         * 自动切换到空闲状态以节省电量
         */
        if (control_value == "10") {
          idle_timeout_duration_ = 10;        // 10分钟超时
        } else if (control_value == "20") {
          idle_timeout_duration_ = 20;        // 20分钟超时
        } else if (control_value == "30") {
          idle_timeout_duration_ = 30;        // 30分钟超时
        } else if (control_value == "40") {
          idle_timeout_duration_ = 40;        // 40分钟超时
        } else if (control_value == "50") {
          idle_timeout_duration_ = 50;        // 50分钟超时
        } else if (control_value == "60") {
          idle_timeout_duration_ = 60;        // 60分钟超时
        }

      } else if (type_val == 3) {
        // === 强制进入空闲状态 (type=3) ===
        ESP_LOGW(TAG, "Entering idle mode, waiting for wake word");

        // 如果当前正在播放音频，先中止播放
        if (device_state_ == kDeviceStateSpeaking) {
          AbortSpeaking(kAbortReasonNone);
        }

        // 可选：关闭音频通道（当前被注释）
        // CloseAudioChannel();

        // 强制切换到空闲状态，等待语音唤醒
        SetDeviceState(kDeviceStateIdle);
      }
    } else if (strcmp(type_str, "iot") == 0) {
      // === IoT设备控制消息处理 ===
      /**
       * IoT控制消息用于控制连接到设备的智能家居设备
       * 支持的设备类型包括：灯光、开关、传感器、空调等
       * 消息格式：{"type": "iot", "commands": [command1, command2, ...]}
       */
      auto commands = cJSON_GetObjectItem(root, "commands");
      if (commands && cJSON_IsArray(commands)) {
        // 获取IoT设备管理器实例
        auto& thing_manager = iot::ThingManager::GetInstance();

        // 遍历并执行所有IoT控制命令
        for (int i = 0; i < cJSON_GetArraySize(commands); ++i) {
          auto command = cJSON_GetArrayItem(commands, i);
          if (command) {
            // 调用IoT设备管理器执行具体的控制命令
            thing_manager.Invoke(command);
          }
        }
      }
    }
  });

  // === 网络协议初始化完成后的设置 ===

  // 可选：播放连接成功提示音（当前被注释）
  // PlayOpusFile(output_link_zh_start, output_link_zh_end - output_link_zh_start);

  // 发送设备唤醒调用到服务器，通知服务器设备已就绪
  protocol_->WakeupCall();

  // 设置设备初始状态为空闲，等待用户交互或语音唤醒
  SetDeviceState(kDeviceStateIdle);


  // === 433MHz无线通信模块初始化（条件编译） ===
  #if Use_433_UART == 1
  /**
   * 433MHz无线通信模块用于设备间的短距离无线通信
   * 主要功能：
   * - 接收来自其他设备的控制指令
   * - 发送设备MAC地址用于设备识别和配对
   * - 支持设备间的状态同步
   */

  // 初始化433MHz UART通信接口
  UART_433_Init();

  /**
   * 创建433MHz通信处理线程
   * - 任务名称: "UART_433_RX_Task"
   * - 堆栈大小: 4KB
   * - 优先级: 1（低优先级后台任务）
   * - 功能: 处理433MHz数据收发
   */
  BaseType_t result = xTaskCreate([](void *){
      // 调试信息：显示任务运行的CPU核心（当前被注释）
      //ESP_LOGI("Thread=UART_433_RX_Task", "--------------UART_433_RX_Task_Thread_Core %d---------------", xPortGetCoreID());

      #if SEND_MAC_ADDRESS_433 == 1
      /**
       * MAC地址发送计数器
       * 设备启动后会定期发送MAC地址，总共发送600次
       * 用于设备发现和网络配对
       */
      static uint16_t uart_433_tx_cnt = 600;
      #endif

      // 433MHz通信主循环
      while (true) {
          // === 接收433MHz数据 ===
          UART_433_RX_DATA();

          #if SEND_MAC_ADDRESS_433 == 1
          // === 发送MAC地址广播 ===
          // 只有在非OTA升级状态下才发送MAC地址
          if (!OTA_UPGRADE_FLAG) {
            // 检查发送计数器，限制发送次数避免频谱污染
            if ((uart_433_tx_cnt > 0) && (uart_433_tx_cnt <= 600)) {
              // 发送设备MAC地址用于设备识别
              UART_433_TX_DATA(SystemInfo::GetMacAddress().c_str());
              uart_433_tx_cnt--;  // 递减计数器
            }
          }
          #endif

          // 任务延时100ms，控制通信频率
          vTaskDelay(100 / portTICK_PERIOD_MS);
      }
  }, "UART_433_RX_Task", 4096, NULL, 1, NULL);

  // 检查433MHz通信任务创建结果
  if (result != pdPASS) {
      ESP_LOGE("UART", "======Failed to create UART 433 RX task!======");
    }else{
        ESP_LOGI("UART", "=======UART RX 433 task created successfully.======");
    }
    #endif


    #if SEND_MAC_ADDRESS_TASK == 1
    // 创建发送MAC地址的任务
    xTaskCreate([](void* arg) {
        // 只在不是升级状态时发送
        auto& app = Application::GetInstance();

        if (app.GetDeviceState() != kDeviceStateUpgrading) {
            for (uint16_t i = 0; i < 600; ++i) { // 600次，每次100ms，共60秒
                UART_433_TX_DATA(SystemInfo::GetMacAddress().c_str());
                vTaskDelay(pdMS_TO_TICKS(100));
            }
            ESP_LOGI("MAC_TX", "MAC TX finished");
        } 
           
        vTaskDelete(NULL);
        },"mac_tx_task",1024,nullptr,2,nullptr
    );
   #endif



   #if SEND_MAC_ADDRESS_TIME == 1
    // 使用定时器发送MAC地址
    static esp_timer_handle_t mac_tx_timer = nullptr;
    static uint16_t mac_tx_count = 0;

    // 定时器回调函数
    auto mac_tx_timer_callback = [](void* arg) {
        auto& app = Application::GetInstance();
        if (app.GetDeviceState() != kDeviceStateUpgrading) {
            UART_433_TX_DATA(SystemInfo::GetMacAddress().c_str());
        }
        mac_tx_count++;
        if (mac_tx_count >= 600) { // 600次，每次100ms，共60秒
            esp_timer_stop(mac_tx_timer);
            esp_timer_delete(mac_tx_timer);
            mac_tx_timer = nullptr;
            ESP_LOGI("MAC_TX", "MAC TX finished (esp_timer)");
        }
    };

    mac_tx_count = 0;
    esp_timer_create_args_t timer_args = {
        .callback = mac_tx_timer_callback,
        .arg = nullptr,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "mac_tx_timer"
    };
    esp_timer_create(&timer_args, &mac_tx_timer);
    esp_timer_start_periodic(mac_tx_timer, 100 * 1000); // 100ms, 单位: 微秒

  #endif

   

}



/**
 * @brief 调度任务到主线程执行
 *
 * 这是应用程序的核心任务调度机制，确保所有状态操作和共享资源访问
 * 都在主线程中安全执行。该方法是线程安全的，可以从任意线程调用。
 *
 * 工作原理：
 * 1. 使用互斥锁保护任务队列的并发访问
 * 2. 将回调函数移动到主任务队列中
 * 3. 设置SCHEDULE_EVENT事件位，唤醒主循环线程
 * 4. 主循环线程会批量执行所有待处理的任务
 *
 * 使用场景：
 * - 设备状态转换（SetDeviceState）
 * - 网络事件处理（连接、断开、数据接收）
 * - 音频事件处理（唤醒词检测、VAD状态变化）
 * - IoT设备状态更新
 * - 用户界面更新
 *
 * @param callback 要在主线程中执行的回调函数
 *
 * @note 回调函数应该避免长时间阻塞操作，以免影响系统响应性
 * @note 使用移动语义避免不必要的函数对象拷贝
 * @thread_safety 线程安全，使用互斥锁保护共享资源
 * @performance 高效的事件驱动机制，避免轮询和忙等待
 *
 * @see MainLoop() 主事件循环，负责执行调度的任务
 * @see SCHEDULE_EVENT 用于唤醒主循环的事件位
 */
void Application::Schedule(std::function<void()> callback) {
  // 使用RAII风格的锁保护，确保异常安全
  std::lock_guard<std::mutex> lock(mutex_);

  // 使用移动语义将回调函数添加到任务队列，避免不必要的拷贝
  main_tasks_.push_back(std::move(callback));

  // 设置事件位，唤醒主循环线程执行待处理的任务
  // 这是一个原子操作，可以安全地从中断上下文调用
  xEventGroupSetBits(event_group_, SCHEDULE_EVENT);
}

/**
 * @brief 音频输入处理循环
 *
 * 这是音频输入处理的核心循环，运行在最高优先级线程中（优先级5），
 * 负责实时处理麦克风输入的音频数据。该循环确保音频处理的实时性，
 * 是整个语音交互系统的关键组件。
 *
 * 主要功能：
 * - 等待音频输入就绪事件（由音频编解码器中断触发）
 * - 调用InputAudio()处理实际的音频数据
 * - 确保音频处理的低延迟和高优先级
 *
 * 线程特性：
 * - 线程名称: "input_audio"
 * - 堆栈大小: 8KB（较大，因为涉及音频处理和算法计算）
 * - 优先级: 5（最高优先级，确保音频实时性）
 *
 * @note 该函数包含无限循环，应在独立的高优先级线程中运行
 * @note 音频处理的实时性要求极高，任何延迟都可能影响用户体验
 * @thread_safety 线程安全，通过事件组同步，不直接访问共享状态
 * @hardware_requirement 需要音频编解码器正确配置并产生AUDIO_INPUT_READY_EVENT事件
 * @see InputAudio() 实际的音频数据处理函数
 */
void Application::InputAudioLoop() {
  while (true) {
    // === 等待音频输入就绪事件 ===

    /**
     * 等待AUDIO_INPUT_READY_EVENT事件
     *
     * 该事件由音频编解码器的输入中断触发，表示有新的音频数据可供处理。
     * 事件触发的时机：
     * - 麦克风采集到新的音频帧
     * - 音频缓冲区达到预设阈值
     * - I2S接口接收到完整的音频数据包
     *
     * 参数说明：
     * - AUDIO_INPUT_READY_EVENT: 等待的事件位
     * - pdTRUE: 等待成功后自动清除事件位，避免重复处理
     * - pdFALSE: 不需要等待所有指定事件
     * - portMAX_DELAY: 无限等待，确保不错过任何音频数据
     */
    auto bits = xEventGroupWaitBits(event_group_, AUDIO_INPUT_READY_EVENT,
                                    pdTRUE, pdFALSE, portMAX_DELAY);

    // === 处理音频输入数据 ===

    if (bits & AUDIO_INPUT_READY_EVENT) {
      /**
       * 调用InputAudio()处理实际的音频数据
       *
       * InputAudio()函数负责：
       * - 从音频编解码器读取原始音频数据
       * - 执行音频预处理（重采样、滤波等）
       * - 将数据传递给音频处理器（AEC、NS等）
       * - 将数据传递给语音唤醒检测器
       */
      InputAudio();
    }
  }
}

/**
 * @brief 应用程序主事件循环
 *
 * 这是应用程序的核心控制循环，负责：
 * - 处理所有通过Schedule()调度的任务
 * - 管理设备状态和网络连接状态
 * - 协调各个功能模块的交互
 * - 监控网络连接状态（条件编译）
 *
 * 该函数运行在独立的主循环线程中，优先级为2（中等优先级）。
 * 所有需要访问设备状态或网络连接的操作都应该通过Schedule()
 * 方法调度到此循环中执行，确保线程安全和状态一致性。
 *
 * @note 该函数包含无限循环，应在独立线程中运行
 * @thread_safety 线程安全，通过事件组和互斥锁保护共享资源
 * @see Schedule() 任务调度方法
 */
void Application::MainLoop() {
  while (true) {
    // === 等待事件触发 ===

    /**
     * 等待事件组中的SCHEDULE_EVENT事件
     * - pdTRUE: 等待成功后自动清除事件位
     * - pdFALSE: 不需要等待所有指定事件（只等待SCHEDULE_EVENT）
     * - portMAX_DELAY: 无限等待，直到事件发生
     */
    auto bits = xEventGroupWaitBits(event_group_,
                                    SCHEDULE_EVENT,
                                    pdTRUE, pdFALSE, portMAX_DELAY);

    // === 处理调度的任务 ===

    // 备用的音频输出事件处理（当前被注释）
    // if (bits & AUDIO_OUTPUT_READY_EVENT) {
    //   OutputAudio();
    // }

    if (bits & SCHEDULE_EVENT) {
      // 使用互斥锁保护任务队列，确保线程安全
      mutex_.lock();

      // 移动语义获取所有待执行任务，避免长时间持有锁
      std::list<std::function<void()>> tasks = std::move(main_tasks_);

      mutex_.unlock();

      // 执行所有调度的任务
      // 注意：任务执行期间不持有锁，允许其他线程继续调度新任务
      for (auto& task : tasks) {
        task();
      }
    }

    // === 网络状态监控（条件编译） ===

    #if LOCAL_MOAN_PLAYING == 1
    /**
     * WiFi连接状态监控
     *
     * 定期检查WiFi连接状态，用于离线模式的切换判断。
     * 当WiFi断开时，设备可以切换到本地音频播放模式。
     */
    wifi_ap_record_t ap_info;
    bool wifi_connected = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);

    if (wifi_connected) {
        NetOff_OFFLINE_FLAG = false;  // WiFi连接正常
    } else {
        NetOff_OFFLINE_FLAG = true;   // WiFi连接断开
    }
    #endif
  }
}


// 测试用电压值（用于电池电压模拟）
double v_test = 4.0;

/**
 * @brief IMU（惯性测量单元）数据读取和运动检测
 *
 * 该函数运行在独立线程中，负责：
 * - 读取QMI8658 IMU传感器数据
 * - 检测设备运动状态（静止、轻微运动、剧烈运动等）
 * - 根据运动状态触发相应的音频播放
 * - 处理433MHz遥控器事件（条件编译）
 * - 管理运动状态变化的防抖和去重
 *
 * 运动检测分级：
 * - MOTION_LEVEL_IDLE: 静止状态
 * - MOTION_LEVEL_1~4: 不同强度的运动状态
 *
 * @note 该函数包含无限循环，运行在"imu_read"线程中
 * @thread_safety 线程安全，使用静态变量记录状态，通过Schedule()与主线程通信
 * @hardware_requirement 需要QMI8658 IMU传感器正确连接和初始化
 */
void Application::ImuRead() {
  // === 静态变量：保持运动状态记录 ===

  // 记录上一次的运动状态，用于状态变化检测和防抖
  static int last_motion_state = MOTION_LEVEL_IDLE;

  // 记录上次发送运动事件的时间戳，用于防止频繁触发
  static int64_t last_send_time = 0;

  // === 局部变量初始化 ===

  // 当前检测到的运动状态
  uint8_t imu_data_motion = MOTION_LEVEL_IDLE;

  // 上次播放的音频文件名，用于避免重复播放
  std::string last_moan_name;

  // === 运动状态与音频文件映射表构建 ===

  /**
   * 构建运动强度到音频文件的映射关系
   *
   * 从全局的kMoaningStringIndex中提取音频文件名，
   * 根据文件名中的数字（运动强度级别）进行分类。
   * 例如：文件名"moan_1_xxx"对应运动强度1
   */
  std::map<int, std::vector<std::string>> imu_motion_moan_string;
  for (const auto& moan_string : kMoaningStringIndex) {
    // 提取文件名中的运动强度数字（第二个字符）
    int motion_level = moan_string.first[1] - '0';
    imu_motion_moan_string[motion_level].push_back(moan_string.first);
  }

  // === 主循环：持续监控IMU数据 ===

  while (true) {
    // === 433MHz遥控器事件处理（备用代码，当前被注释） ===

    /*
     * 433MHz无线遥控器事件处理逻辑
     * 可以接收外部遥控器的按键事件，用于远程控制设备
     */
    // 先尝试从队列取433事件（非阻塞或带超时）
    // if (xQueueReceive(uart_433_event_queue, &event, 0)) {
    //     button_value_int_local = event.button_value_int;
    //     key_433_press_local = event.key_press;
    // }

    // 调试日志（当前被注释）
    // ESP_LOGI("IMU", "Received control_moan message: %d", moaning_flag_);

    // ========================================================================
    // IMU传感器数据读取和电池监控任务
    // ========================================================================

    /**
     * 任务执行间隔控制
     *
     * 设置500ms的执行间隔，平衡系统响应性和CPU占用：
     * - 减少CPU占用，避免频繁的传感器读取
     * - 保证足够的响应速度用于姿态检测
     * - 为其他高优先级任务留出CPU时间
     */
    // vTaskDelay(300);  // 原300ms间隔，已优化为500ms
    vTaskDelay(500 / portTICK_PERIOD_MS);  // 当前500ms执行一次

    /**
     * 电池电压采集
     *
     * 通过硬件抽象层获取当前电池电压值，用于：
     * - 电池电量监控
     * - 低电量报警
     * - 电源管理决策
     */
    double vo = Board::GetInstance().GetBattary();

    // 测试代码：模拟电池电压下降（已注释）
    // if (v_test > 2.0) {
    //   v_test -= 0.02;
    // }
    // vo = v_test;
    // // batter_volo_-=0.02;

    /**
     * 电池电压滤波处理
     *
     * 使用一阶低通滤波器平滑电池电压读数：
     * - 滤波系数：0.85（历史值权重）+ 0.15（当前值权重）
     * - 目的：消除电压读数的瞬时波动
     * - 提高电池状态判断的稳定性
     */
    int low_batter_audio_play_cnt = 0;  // 低电量音频播放计数器
    batter_volo_ = batter_volo_ * 0.85 + vo * 0.15;  // 低通滤波

    // 调试代码：GPIO48电平检测和电压日志（已注释）
    // int level = gpio_get_level(GPIO_NUM_48);
    // ESP_LOGI("GPIO", "GPIO48 level: %d", level);
    // ESP_LOGW("BAT", "battery voltage:%f", batter_volo_);

    /**
     * 电池状态监控和低电量处理
     *
     * 监控逻辑：
     * 1. 检查GPIO48电平（可能是充电检测引脚）
     * 2. 根据电池电压判断电池状态
     * 3. 低电量时播放提示音
     * 4. 极低电量时触发关机保护
     */
    if ((gpio_get_level(GPIO_NUM_48) == 0)) {  // GPIO48为低电平时（可能表示未充电）

      /**
       * 低电量检测和处理（电压 < 3.6V）
       */
      if (batter_volo_ < 3.6) {
        // ESP_LOGW("BAT", "battery voltage:%f", batter_volo_);
        batter_state_ = true;  // 设置低电量状态标志

        /**
         * 低电量音频提示
         *
         * 条件：
         * - 设备处于空闲状态
         * - 计数器达到阈值（避免频繁播放）
         */
        if (device_state_ == kDeviceStateIdle) {
          ESP_LOGW("BAT", "battery voltage:%f", batter_volo_);
          // codec->EnableInput(false);  // 可选：关闭麦克风输入以节省电量

          /**
           * 低电量提示音播放控制
           *
           * 使用计数器机制避免频繁播放：
           * - 每5次检测（约2.5秒）播放一次提示音
           * - 播放完成后重置计数器
           */
          if (++low_batter_audio_play_cnt > 4) {
            low_batter_audio_play_cnt = 0;
            Schedule([this]() {
              if (oudio_output_finish_) {  // 确保上一个音频播放完成
                oudio_output_finish_ = false;

                // 播放低电量提示音（"dong"音效）
                PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);
              }
            });
          }
        }
      }

      /**
       * 极低电量保护（电压 < 3.3V）
       *
       * 当电池电压过低时，触发关机保护机制：
       * - 防止电池过度放电损坏
       * - 保护硬件安全
       */
      if (batter_volo_ < 3.3) {
        SetPowerOffset(0);  // 触发关机或深度睡眠
      }
      /**
       * 电池电压恢复处理（电压 > 3.6V）
       */
      else if (batter_volo_ > 3.6) {
        batter_state_ = false;  // 清除低电量状态
        low_batter_audio_play_cnt = 0;  // 重置计数器
      }
    } else {
      /**
       * GPIO48为高电平时（可能表示正在充电）
       * 清除低电量状态标志
       */
      batter_state_ = false;
    }

    // ========================================================================
    // 语音播放状态监控和超时处理（已禁用）
    // ========================================================================

    /**
     * 语音播放超时保护机制（当前已注释禁用）
     *
     * 目的：防止网络异常导致的语音播放状态异常
     *
     * 工作原理：
     * - start_play_voice_：标志当前是否处于"正在播放语音"状态
     * - oudio_output_finish_：标志音频输出是否已经完成（即音频播放完毕）
     * - start_play_voice_num_：超时计数器
     *
     * 超时逻辑：
     * - 每500ms检查一次状态
     * - 如果连续20次（10秒）检测到播放状态异常
     * - 自动重置为监听状态，防止系统卡死
     */
    // 防止开始了start后网络原因没有发stop下来
    //start_play_voice_：标志当前是否处于“正在播放语音”状态。
    //oudio_output_finish_：标志音频输出是否已经完成（即音频播放完毕）。
    // Schedule([this]() {
    //   if (start_play_voice_ && oudio_output_finish_) {
    //     //start_play_voice_num_：计数器，
    //     //每次检测到 start_play_voice_ && oudio_output_finish_ 时自增
    //     //500ms 进入一次， 20 *500ms = 10000ms = 10s
    //     if (start_play_voice_num_++ > 20) {
    //       SetDeviceState(kDeviceStateListening);
    //       ESP_LOGI("[TEST_07]", "**************** TEST_07 *****************");
    //       start_play_voice_ = 0;
    //       start_play_voice_num_ = 0;
    //     }
    //   }
    // });

    // ========================================================================
    // LED状态更新
    // ========================================================================

    /**
     * 电池状态变化时更新LED显示
     *
     * 当电池状态发生变化时：
     * - 通知LED控制器更新显示状态
     * - 可能改变LED颜色或闪烁模式来指示电池状态
     * - 提供直观的电池状态反馈
     */
    if (last_batter_state_ != batter_state_) {
      auto led = Board::GetInstance().GetLed();
      led->OnStateChanged();  // 触发LED状态更新
    }
    last_batter_state_ = batter_state_;  // 更新上次电池状态记录

    // ========================================================================
    // IMU传感器数据采集和运动检测
    // ========================================================================

    /**
     * IMU传感器数据读取
     *
     * 从QMI8658 IMU传感器获取运动数据：
     * - 加速度计数据
     * - 陀螺仪数据
     * - 运动状态分析结果
     * - 姿态角度信息
     */
    auto imu_data = qmi8658_motion_demo();
    int current_motion = imu_data.motion;  // 当前运动状态
    int64_t current_time = esp_timer_get_time() / 1000;  // 当前时间戳(毫秒)
    //
    if (moaning_state_.first) {
      // if (current_motion != last_motion_state) {
      //   if (current_motion == MOTION_LEVEL_IDLE) {
      //     Schedule([this, current_motion, &last_moan_name]() {
      //       SetDeviceState(kDeviceStateListening);
      //     });
      //     audio_decode_queue_->clear();
      //     last_moan_name.clear();
      //   } else if (moaning_state_.second.empty()) {
      //     int rand_moan_index =
      //         rand() % imu_motion_moan_string[current_motion].size();
      //     const std::string rand_moan_name =
      //         imu_motion_moan_string[current_motion][rand_moan_index];
      //     Schedule([this, rand_moan_name]() {
      //       SetDeviceState(kDeviceStateMoaning);
      //       audio_decode_queue_->LoopPlayLocalFile(
      //           kMoaningStringIndex[rand_moan_name].first,
      //           kMoaningStringIndex[rand_moan_name].second);
      //     });
      //   } else if (last_moan_name != moaning_state_.second) {
      //     Schedule([this, current_motion, &last_moan_name]() {
      //       SetDeviceState(kDeviceStateMoaning);
      //       audio_decode_queue_->LoopPlayLocalFile(
      //           kMoaningStringIndex[moaning_state_.second].first,
      //           kMoaningStringIndex[moaning_state_.second].second);
      //     });
      //     last_moan_name = moaning_state_.second;
      //   }
      // }
    }

    // ========================================================================
    // 触摸按键数据采集（条件编译）
    // ========================================================================

    /**
     * 触摸按键数据获取
     *
     * 根据编译配置选择不同的输入方式：
     * - FF_IMU_PARAM_IS_Wire == 1: 有线模式，使用触摸按键
     * - FF_IMU_PARAM_IS_Wire == 0: 无线模式，使用按钮输入
     */
    #if FF_IMU_PARAM_IS_Wire == 1
    auto touch_value = Board::GetInstance().GetTouchKey();  // 获取触摸按键状态

    #elif FF_IMU_PARAM_IS_Wire == 0
    //auto touch_value = button_value_int;  // 无线模式下使用按钮值
    #endif

    // ========================================================================
    // IMU数据发送频率控制（已禁用）
    // ========================================================================

    /**
     * IMU数据发送频率限制机制（当前已注释禁用）
     *
     * 目的：避免频繁发送IMU数据造成网络拥塞
     *
     * 限制策略：
     * - 只有运动状态发生变化时才考虑发送
     * - 两次发送之间至少间隔600ms
     * - 防止高频率的状态抖动
     */
    // 判断与上次状态是否不同且满足最小发送间隔
    if (current_motion != last_motion_state &&
        (current_time - last_send_time > 600)) {  // 至少600ms发送一次

    //   // 状态变化且满足时间间隔，发送
    //   Schedule([this, current_motion]() {
    //     if (!protocol_) return;

    //     // 记录并发送新状态
    //     if (current_motion == 0) {
    //       ESP_LOGW("IMU", "Motion changed to IDLE (0)");
    //     } else {
    //       ESP_LOGW("IMU", "Motion changed to: %d", current_motion);
    //     }

    //     protocol_->SendImuStatesAndValue(current_motion);
    //   });

    //   // 更新状态和发送时间
    //   last_send_time = current_time;
    // } else if (current_motion != last_motion_state) {
    //   // 只更新状态，不发送
    // }
    }
    

    // ========================================================================
    // 触摸按键数据处理和编码
    // ========================================================================

    /**
     * 触摸按键数据编码处理
     *
     * 根据不同的硬件配置处理输入数据：
     */
    #if FF_IMU_PARAM_IS_Wire == 1
    /**
     * 有线模式：多点触摸按键处理
     *
     * 将多个触摸点的状态编码为一个整数：
     * - 使用位操作将每个触摸点状态压缩到一个bit
     * - 从高位到低位依次编码各个触摸点
     * - 最终得到一个表示所有触摸状态的整数值
     */
    int touch_value_temp = 0;
    for (int i = 0; i < touch_value.size(); i++) {
      // ESP_LOGW(TAG, "touch_value %d",int( touch_value[i]));
      touch_value_temp <<= 1;  // 左移一位，为新的触摸点状态腾出空间
      if (touch_value[i]) {
        touch_value_temp |= 0x01;  // 如果触摸点被按下，设置最低位为1
      }
    }

    #elif FF_IMU_PARAM_IS_Wire == 0
    /**
     * 无线模式：按钮值处理
     *
     * 对按钮值进行简单的数值处理：
     * - 将原始按钮值除以2进行缩放
     * - 可能用于降低按钮敏感度或进行数值归一化
     */
    auto touch_value_temp = (button_value_int/2);
    //if(touch_value_temp == button_value_int) first_key = true;  // 首次按键检测（已注释）

    #endif
    

    // ========================================================================
    // 有线模式：IMU和触摸数据发送
    // ========================================================================

    #if FF_IMU_PARAM_IS_Wire == 1
    /**
     * 有线模式下的数据发送逻辑
     *
     * 发送条件：
     * - IMU运动状态发生变化，或
     * - 触摸按键状态发生变化
     *
     * 发送内容：
     * - IMU传感器完整数据（包括加速度、陀螺仪、运动状态等）
     * - 触摸按键编码后的状态值
     */
    // ESP_LOGW(TAG, "touch_value %d",int( touch_value_temp));
    // ESP_LOGW(TAG, "touch_value %d", touch_value_temp);
    if (current_motion != last_motion_state ||
        touch_value_temp != touch_value_) {
      Schedule([this, imu_data, touch_value_temp]() {
        if (!protocol_) return;  // 确保协议对象有效

        // 调试日志（已注释）
        // // 记录并发送新状态
        // if (current_motion == 0) {
        //   ESP_LOGW("IMU", "Motion changed to IDLE (0)");
        // } else {
        //   ESP_LOGW("IMU", "Motion changed to: %d", current_motion);
        // }

        // 发送IMU数据和触摸状态到服务器
        protocol_->SendImuStatesAndValue(imu_data, touch_value_temp);
      });
    }


    // ========================================================================
    // 无线模式：本地音频播放控制
    // ========================================================================

    #elif FF_IMU_PARAM_IS_Wire == 0  // 无线模式下

    /**
     * 本地音频播放功能（条件编译）
     *
     * 当启用LOCAL_MOAN_PLAYING时，设备可以在离线状态下
     * 根据IMU运动检测播放本地存储的音频文件
     */
    #if LOCAL_MOAN_PLAYING == 1

    /**
     * 音频选择策略（条件编译选择）
     */
    #if 0
    /**
     * 策略1：顺序递增选择（已禁用）
     *
     * 每次运动状态变化时，音频计数器递增
     * 计数器达到254时重置为1，实现循环播放
     */
    if ((current_motion != last_motion_state)) {
        moan_count += 1;
        if(moan_count >= 254) moan_count = 1;
    }
    #else
    /**
     * 策略2：随机选择音频（当前启用）
     *
     * 使用硬件随机数生成器选择音频文件：
     * - 随机范围：10-70（对应不同的音频文件）
     * - 触发条件：IMU运动状态变化或433MHz按键按下
     * - 增加音频播放的随机性和多样性
     */
    ///使用随机数来选择音频
    if ((current_motion != last_motion_state)) {
      // IMU状态变化触发
      IMU_CHANGE_FLAG = true;  // 设置IMU变化标志
      moan_count = (uint8_t)(esp_random() % 61) + 10;  // 生成10-70的随机数

    } else if(key_433_press == true) {
      // 433MHz无线按键触发
      IMU_CHANGE_FLAG = true;  // 设置IMU变化标志
      moan_count = (uint8_t)(esp_random() % 61) + 10;  // 生成10-70的随机数
      button_value_int = 0;    // 重置按钮值
      key_433_press = false;   // 清除按键标志
    }

    #endif
    #endif
    // ========================================================================
    // 离线模式本地音频播放逻辑
    // ========================================================================

    /**
     * 离线状态检查
     *
     * 双重离线标志检查：
     * - OFFLINE_FLAG: 设备离线标志
     * - NetOff_OFFLINE_FLAG: 网络断开标志
     *
     * 只有在完全离线状态下才启用本地音频播放
     */
    //ESP_LOGW("IMU_DATA", "--=====ooooo=====-- IMU Data=  %s --======ooooo=====-- ", imu_data.ToString().c_str());
    if (OFFLINE_FLAG && NetOff_OFFLINE_FLAG) {

      /**
       * 本地音频播放功能实现
       *
       * 根据随机生成的moan_count值选择对应的音频文件进行播放
       * 音频文件范围：p3_moan_1000020 到 p3_moan_1000031（共12个音频文件）
       */
      #if LOCAL_MOAN_PLAYING == 1
      #if 1  // 启用当前的音频播放实现

      ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--moan_count : %d--======ooooo=====-- ",moan_count);

      /**
       * 音频文件1播放逻辑（moan_count: 10-14）
       *
       * 播放条件：
       * - moan_count在10-14范围内
       * - 当前没有音频在播放（local_moan_is_playing == false）
       * - IMU状态发生了变化（IMU_CHANGE_FLAG == true）
       *
       * 播放流程：
       * 1. 重置音频输出完成标志
       * 2. 播放指定的OPUS音频文件
       * 3. 等待播放完成（最多60秒超时）
       * 4. 设置播放状态标志
       * 5. 清空音频队列
       * 6. 重置IMU变化标志
       */
      if((moan_count >= 10 && moan_count < 15)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 01 --======ooooo=====-- ");
        oudio_output_finish_ = false;  // 1. 重置音频输出完成标志
        PlayOpusFile(p3_moan_1000020_start, p3_moan_1000020_end - p3_moan_1000020_start);  // 2. 播放音频文件
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {  // 3. 等待播放完成，最多60秒
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;  // 4. 设置播放状态标志
        audio_decode_queue_->clear();  // 5. 清空音频队列
        IMU_CHANGE_FLAG = false;       // 6. 重置IMU变化标志
                  
      /**
       * 音频文件2播放逻辑（moan_count: 15-19）
       * 播放p3_moan_1000021音频文件，流程与音频文件1相同
       */
      } else if((moan_count >= 15 && moan_count < 20)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 02 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000021_start, p3_moan_1000021_end - p3_moan_1000021_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件3播放逻辑（moan_count: 20-24）
       * 播放p3_moan_1000022音频文件
       */
      } else if((moan_count >= 20 && moan_count < 25)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 03 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000022_start, p3_moan_1000022_end - p3_moan_1000022_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件4播放逻辑（moan_count: 25-29）
       * 播放p3_moan_1000023音频文件
       */
      } else if((moan_count >= 25 && moan_count < 30)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 04 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000023_start, p3_moan_1000023_end - p3_moan_1000023_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件5播放逻辑（moan_count: 30-34）
       * 播放p3_moan_1000024音频文件
       */
      } else if((moan_count >= 30 && moan_count < 35)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 05 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000024_start, p3_moan_1000024_end - p3_moan_1000024_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件6播放逻辑（moan_count: 35-39）
       * 播放p3_moan_1000025音频文件
       */
      } else if((moan_count >= 35 && moan_count < 40)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 06 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000025_start, p3_moan_1000025_end - p3_moan_1000025_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件7播放逻辑（moan_count: 40-44）
       * 播放p3_moan_1000026音频文件
       */
      } else if((moan_count >= 40 && moan_count < 45)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 07 --======ooooo=====-- ");
        oudio_output_finish_ = false; // 1. 重置音频输出完成标志
        PlayOpusFile(p3_moan_1000026_start, p3_moan_1000026_end - p3_moan_1000026_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) { // 3. 等待播放完成，最多60秒
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件8播放逻辑（moan_count: 45-49）
       * 播放p3_moan_1000027音频文件
       */
      } else if((moan_count >= 45 && moan_count < 50)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 08 --======ooooo=====-- ");
        oudio_output_finish_ = false; // 1. 重置音频输出完成标志
        PlayOpusFile(p3_moan_1000027_start, p3_moan_1000027_end - p3_moan_1000027_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件9播放逻辑（moan_count: 50-54）
       * 播放p3_moan_1000028音频文件
       */
      } else if((moan_count >= 50 && moan_count < 55)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 09 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000028_start, p3_moan_1000028_end - p3_moan_1000028_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件10播放逻辑（moan_count: 55-59）
       * 播放p3_moan_1000029音频文件
       */
      } else if((moan_count >= 55 && moan_count < 60)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 10 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000029_start, p3_moan_1000029_end - p3_moan_1000029_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件11播放逻辑（moan_count: 60-64）
       * 播放p3_moan_1000030音频文件
       */
      } else if((moan_count >= 60 && moan_count < 65)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 11 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000030_start, p3_moan_1000030_end - p3_moan_1000030_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频文件12播放逻辑（moan_count: 65-70）
       * 播放p3_moan_1000031音频文件（最后一个音频文件）
       */
      } else if((moan_count >= 65 && moan_count <= 70)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
        ESP_LOGW("Local_moan", "--=====ooooo=====--moan 012 --======ooooo=====-- ");
        oudio_output_finish_ = false;
        PlayOpusFile(p3_moan_1000031_start, p3_moan_1000031_end - p3_moan_1000031_start);
        uint16_t wait_ms = 0;
        while (!oudio_output_finish_ && wait_ms < 60000) {
            vTaskDelay(pdMS_TO_TICKS(100));
            wait_ms += 100;
        }
        local_moan_is_playing = true;
        audio_decode_queue_->clear();
        IMU_CHANGE_FLAG = false;

      /**
       * 音频播放完成检测
       *
       * 当音频播放完成时，重置播放状态标志
       * 允许下次运动检测时重新播放音频
       */
      } else if (local_moan_is_playing && oudio_output_finish_) {
          local_moan_is_playing = false;  // 重置播放状态，允许下次播放
      }


      ESP_LOGW("END_LOCAL_MOAN", "--=====ooooo=====--END_LOCAL_MOAN--======ooooo=====-- ");

      // ========================================================================
      // 备用的本地音频播放实现（已禁用）
      // ========================================================================

      #else
      /**
       * 备用的本地音频播放实现（当前已禁用）
       *
       * 这是另一种本地音频播放的实现方式：
       * - 使用顺序递增的计数器选择音频
       * - 只在特定计数值时播放音频
       * - 相比随机选择方式，播放频率更低
       */
      if ((current_motion != last_motion_state)) {
        ESP_LOGW("IMU_DATA", "--=====ooooo=====-- IMU Data=  %s --======ooooo=====-- ", imu_data.ToString().c_str());
        moan_count += 1;  // 计数器递增
        if(moan_count >= 254) moan_count = 1;  // 计数器溢出重置

        ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--moan_count : %d--======ooooo=====-- ",moan_count);

        /**
         * 测试音频播放（计数器 == 3时）
         *
         * 播放流程：
         * 1. 重置音频输出完成标志
         * 2. 播放指定音频文件
         * 3. 等待播放完成（最多60秒）
         * 4. 设置播放状态标志
         * 5. 清空音频队列
         */
        //// -------------------------音频1
        if ((moan_count == 3) && (local_moan_is_playing == false)) {
          oudio_output_finish_ = false; // 1. 重置标志
          PlayOpusFile(p3_moan_1000022_start, p3_moan_1000022_end - p3_moan_1000022_start); // 2. 播放音频
          uint16_t wait_ms = 0;
          while (!oudio_output_finish_ && wait_ms < 60000) { // 3. 等待播放完成，最多60秒
              vTaskDelay(pdMS_TO_TICKS(100));
              wait_ms += 100;
          }
          local_moan_is_playing = true; // 4. 设置播放状态
          audio_decode_queue_->clear(); // 5. 清空音频队列
        }

        /**
         * 其他音频播放实现方式（已注释禁用）
         *
         * 以下是各种不同的音频播放实现尝试，包括：
         * 1. 简化的播放方式（使用固定延时）
         * 2. 手动控制音频编解码器的播放方式
         * 3. 基于特定计数值的音频选择方式
         */
        // if ((moan_count == 3) && (local_moan_is_playing == false)) {
        //   PlayOpusFile(p3_moan_1000022_start, p3_moan_1000022_end - p3_moan_1000022_start);
        //   vTaskDelay(pdMS_TO_TICKS(7200));  // 固定延时7.2秒
        //   local_moan_is_playing = true;
        // }

        /**
         * 手动控制音频编解码器的播放方式（已注释）
         *
         * 这种方式手动控制音频输出的开启和关闭：
         * - 播放前确保音频输出开启
         * - 播放完成后关闭音频输出以节省电量
         */
        // 通过位操作直接调用对应音频
        // 每个位对应一个音频资源（仅12个）
        // if (moan_count == 3){

        // auto codec = Board::GetInstance().GetAudioCodec();
        // codec->EnableOutput(true); // 确保输出开启
        // oudio_output_finish_ = false;
        // PlayOpusFile(p3_moan_1000021_start, p3_moan_1000021_end - p3_moan_1000021_start);
        // int wait_ms = 0;
        // while (!oudio_output_finish_ && wait_ms < 60000) {
        //     vTaskDelay(pdMS_TO_TICKS(100));
        //     wait_ms += 100;
        // }
        // codec->EnableOutput(false); // 播放完再关闭

        //   }

        /**
         * 基于特定计数值的多音频播放方式（已注释）
         *
         * 这种方式在特定的计数值时播放不同的音频文件：
         * - 计数值23: 播放音频22
         * - 计数值43: 播放音频23
         * - 计数值63: 播放音频24
         * - 以此类推...
         *
         * 这种方式可以实现更稀疏的音频播放频率
         */
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--21--======ooooo=====-- ");}
        //  // vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 23) PlayOpusFile(p3_moan_1000022_start, p3_moan_1000022_end - p3_moan_1000022_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--22--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 43) PlayOpusFile(p3_moan_1000023_start, p3_moan_1000023_end - p3_moan_1000023_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--23--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 63) PlayOpusFile(p3_moan_1000024_start, p3_moan_1000024_end - p3_moan_1000024_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--24--======ooooo=====-- ");
        //  // vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 83) PlayOpusFile(p3_moan_1000025_start, p3_moan_1000025_end - p3_moan_1000025_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--25--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 103) PlayOpusFile(p3_moan_1000026_start, p3_moan_1000026_end - p3_moan_1000026_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--26--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 123) PlayOpusFile(p3_moan_1000027_start, p3_moan_1000027_end - p3_moan_1000027_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--27--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 143) PlayOpusFile(p3_moan_1000028_start, p3_moan_1000028_end - p3_moan_1000028_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--28--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 163) PlayOpusFile(p3_moan_1000029_start, p3_moan_1000029_end - p3_moan_1000029_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--29--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 183) PlayOpusFile(p3_moan_1000030_start, p3_moan_1000030_end - p3_moan_1000030_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--30--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
        //   if (moan_count == 203) PlayOpusFile(p3_moan_1000031_start, p3_moan_1000031_end - p3_moan_1000031_start);
        //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--31--======ooooo=====-- ");
        //   //vTaskDelay(pdMS_TO_TICKS(10000));
      }
      /**
       * 433MHz按键处理
       *
       * 当433MHz无线按键被按下时：
       * - 重置按钮值
       * - 清除按键按下标志
       */
      else if (key_433_press == true) {
        button_value_int = 0;
        key_433_press = false;
      }
      #endif  // LOCAL_MOAN_PLAYING
      #endif  // 条件编译结束
    }
    // ========================================================================
    // 在线模式：网络数据发送
    // ========================================================================
    else {
      /**
       * 在线模式下的IMU和触摸数据发送
       *
       * 当设备处于在线状态时，将IMU和触摸数据发送到服务器：
       * - 发送条件：IMU运动状态变化或433MHz按键按下
       * - 发送内容：完整的IMU数据和触摸状态
       * - 可选的moaning模式控制
       */
      // 非离线模式（在线模式）
      if ((current_motion != last_motion_state) || (key_433_press == true)) {
        ESP_LOGW("application", "----------** Key_433_Press=  %d **------- ", key_433_press);
        Schedule([this, imu_data, touch_value_temp]() {
          if (!protocol_) return;  // 确保协议对象有效

          /**
           * Moaning模式控制（条件编译）
           *
           * 当启用moaning模式控制时，只有在moaning_flag_为true时
           * 才发送IMU数据到服务器，否则无条件发送
           */
          #if 1
          ESP_LOGW("IMU", "===============moaning_flag_ : %d=================",moaning_flag_);
          if (moaning_flag_ == true) {
            protocol_->SendImuStatesAndValue(imu_data, touch_value_temp);
          }
          #else
          // 无条件发送IMU数据（备用实现）
          protocol_->SendImuStatesAndValue(imu_data, touch_value_temp);
          #endif
        });

        // 重置按键相关状态
        button_value_int = 0;
        key_433_press = false;
      }
    }
    #endif  // FF_IMU_PARAM_IS_Wire

    // ========================================================================
    // 状态更新和清理
    // ========================================================================

    /**
     * 更新状态变量
     *
     * 保存当前状态以供下次循环比较：
     * - 触摸按键状态
     * - IMU运动状态
     */
    touch_value_ = touch_value_temp;  // 更新触摸状态
    last_motion_state = current_motion;  // 更新运动状态

    // 备用的IMU数据发送方式（已注释）
    // protocol_->SendImuStatesAndValue(current_motion);
  }
}



/**
 * @brief 音频输出处理函数（当前为空实现）
 *
 * 这个函数原本用于处理音频输出相关的操作，但当前实现为空。
 * 可能的用途包括：
 * - 音频输出缓冲区管理
 * - 音频输出时序控制
 * - 音频输出状态监控
 *
 * @note 当前版本中音频输出通过其他机制处理
 * @see audio_decode_queue_ 音频解码队列处理音频播放
 * @see PlayOpusFile() 直接播放OPUS文件的方法
 */
void Application::OutputAudio() {
  auto codec = Board::GetInstance().GetAudioCodec();  // 获取音频编解码器实例
  auto now = std::chrono::steady_clock::now();        // 获取当前时间戳

  // 当前实现为空，音频输出通过其他机制处理
}

/**
 * @brief 重置音频解码器状态
 *
 * 重置音频解码相关的所有状态和缓冲区，用于以下场景：
 * - 设备状态切换时清理音频状态
 * - 开始新的对话会话前的初始化
 * - 音频通道重新建立时的状态重置
 * - 错误恢复时的状态清理
 *
 * 执行的操作：
 * 1. 清空音频解码播放队列
 * 2. 重置相关的状态标志和时间戳
 * 3. 确保音频输出设备处于正确状态
 *
 * @note 该函数主要清理AudioDecodeQueue，OPUS解码器由AudioDecodeQueue内部管理
 * @note 当前实现已简化，大部分功能由AudioDecodeQueue内部处理
 * @thread_safety 线程安全，AudioDecodeQueue内部实现了线程保护
 *
 * @see AudioDecodeQueue::clear() 清空音频播放队列
 * @see SetDeviceState() 状态切换时会调用此函数
 */
void Application::ResetDecoder() {
  auto codec = Board::GetInstance().GetAudioCodec();

  // === 历史实现（已被注释，保留用于参考） ===

  // 以下代码展示了之前的完整实现，现在由AudioDecodeQueue统一管理：

  // 设置解码器采样率为16kHz（与编码时相同）
  // opus_decode_sample_rate_ = 16000;
  // opus_decoder_ = std::make_unique<OpusDecoderWrapper>(opus_decode_sample_rate_, 1);

  // 确保有锁保护清空队列操作
  // {
  //   std::lock_guard<std::mutex> lock(mutex_);

  // === 当前实现：清空音频解码播放队列 ===

  /**
   * 清空音频解码播放队列
   *
   * AudioDecodeQueue内部包含：
   * - OPUS解码器实例
   * - PCM音频缓冲区
   * - 播放状态管理
   * - 线程安全保护
   */
  audio_decode_queue_->clear();

  // }

  // === 历史状态重置（已被注释） ===

  // 重置音频输出时间戳，用于音频同步
  // last_output_time_ = std::chrono::steady_clock::now();

  // 重置中止标志，允许新的音频播放
  // aborted_ = false;

  // 重置播放后停止标志
  // stop_after_playback_ = false;

  // 确保音频输出已启用并设置正确的音量
  // codec->EnableOutput(true);
  // codec->SetOutputVolume(80);
}

/**
 * @brief 音频输入数据处理
 *
 * 这是音频输入处理的核心函数，负责从音频编解码器读取原始音频数据，
 * 执行必要的预处理，然后将数据传递给音频处理器和语音唤醒检测器。
 * 该函数在最高优先级的音频输入线程中被调用，确保实时性。
 *
 * 主要处理流程：
 * 1. 从音频编解码器读取原始PCM数据
 * 2. 执行重采样（如果需要）将采样率转换为16kHz
 * 3. 处理多声道数据（分离麦克风和参考信号）
 * 4. 根据设备状态将数据传递给相应的处理模块
 *
 * 性能优化：
 * - 使用静态缓冲区避免频繁的内存分配
 * - 优化的重采样算法减少计算开销
 * - 条件编译支持不同平台的处理策略
 *
 * @note 该函数在音频输入线程中被调用，具有最高优先级
 * @note 16kHz是语音处理的标准采样率，平衡了音质和计算复杂度
 * @thread_safety 线程安全，在专用的音频输入线程中运行
 * @performance 高度优化，使用静态缓冲区和高效算法
 * @hardware_requirement 需要音频编解码器正确配置和工作
 */
void Application::InputAudio() {
  // === 获取音频编解码器实例 ===
  auto codec = Board::GetInstance().GetAudioCodec();

  // === 读取原始音频数据 ===
  std::vector<int16_t> data;
  if (!codec->InputData(data)) {
    // 如果没有可用的音频数据，直接返回
    // 这可能发生在音频编解码器未就绪或硬件故障时
    return;
  }

  // === 音频重采样处理 ===

  /**
   * 重采样优化逻辑
   *
   * 如果音频编解码器的采样率不是16kHz，需要进行重采样。
   * 16kHz是语音处理的标准采样率，ESP-SR库和OPUS编码器都基于此采样率。
   * 使用静态缓冲区避免频繁的内存分配，提高实时性能。
   */
  if (codec->input_sample_rate() != 16000) {
    static std::vector<int16_t> resampled_buffer;  // 静态缓冲区，重用内存避免频繁分配

    if (codec->input_channels() == 2) {
      // === 双声道处理：分离麦克风和参考信号 ===

      static std::vector<int16_t> mic_channel;        // 麦克风信号缓冲区
      static std::vector<int16_t> reference_channel;  // 参考信号缓冲区（用于AEC回声消除）

      // 调整缓冲区大小以容纳分离后的单声道数据
      mic_channel.resize(data.size() / 2);
      reference_channel.resize(data.size() / 2);

      // 分离交错的双声道数据
      // 输入格式：[L0, R0, L1, R1, ...] → 输出：[L0, L1, ...], [R0, R1, ...]
      for (size_t i = 0, j = 0; i < mic_channel.size(); ++i, j += 2) {
        mic_channel[i] = data[j];        // 左声道：麦克风信号
        reference_channel[i] = data[j + 1];  // 右声道：参考信号（扬声器输出）
      }

      // 计算重采样后的缓冲区大小（双声道）
      resampled_buffer.resize(
          input_resampler_.GetOutputSamples(mic_channel.size()) * 2);

      // 创建重采样输出缓冲区的视图，避免额外的内存分配
      auto resampled_mic = std::vector<int16_t>(
          resampled_buffer.begin(),
          resampled_buffer.begin() + resampled_buffer.size() / 2);
      auto resampled_reference = std::vector<int16_t>(
          resampled_buffer.begin() + resampled_buffer.size() / 2,
          resampled_buffer.end());

      // 分别对麦克风和参考信号进行重采样到16kHz
      input_resampler_.Process(mic_channel.data(), mic_channel.size(),
                               resampled_mic.data());
      reference_resampler_.Process(reference_channel.data(),
                                   reference_channel.size(),
                                   resampled_reference.data());

      // 重新交错重采样后的数据
      // 输出格式：[L0, R0, L1, R1, ...]
      for (size_t i = 0, j = 0; i < resampled_mic.size(); ++i, j += 2) {
        resampled_buffer[j] = resampled_mic[i];
        resampled_buffer[j + 1] = resampled_reference[i];
      }
      data = std::move(resampled_buffer);

    } else {
      // === 单声道处理：直接重采样 ===

      resampled_buffer.resize(input_resampler_.GetOutputSamples(data.size()));
      input_resampler_.Process(data.data(), data.size(),
                               resampled_buffer.data());
      data = std::move(resampled_buffer);
    }
  }

  // === 音频数据分发处理 ===

  #if CONFIG_IDF_TARGET_ESP32S3
  /**
   * ESP32S3平台的音频处理策略
   *
   * ESP32S3具有专用的音频处理器和语音唤醒检测器，
   * 可以同时进行语音唤醒检测和音频信号处理。
   */

  // === 分离麦克风和参考信号（用于AEC） ===
  std::vector<int16_t> mic_data, ref_data;
  if (codec->input_channels() == 2 && codec->input_reference()) {
    /**
     * 双声道模式：分离麦克风和参考信号
     *
     * 数据格式：[M0,R0,M1,R1,M2,R2,...]
     * M = 麦克风信号（通道0）
     * R = 参考信号（通道1，设备播放的声音）
     */
    mic_data.reserve(data.size() / 2);
    ref_data.reserve(data.size() / 2);

    for (size_t i = 0; i < data.size(); i += 2) {
      mic_data.push_back(data[i]);     // 通道0：麦克风
      ref_data.push_back(data[i + 1]); // 通道1：参考信号
    }
  } else {
    /**
     * 单声道模式：只有麦克风数据
     * 参考信号为空，AEC将被禁用
     */
    mic_data = data;
    ref_data.clear(); // 空的参考信号
  }

  // === 播放状态下的特殊处理 ===
  if (device_state_ == kDeviceStateSpeaking || device_state_ == kDeviceStateMoaning) {
    /**
     * 播放状态下只进行语音唤醒检测
     *
     * 原因：
     * 1. 避免设备自己的播放声音被误录入和处理
     * 2. 但仍需要检测用户的唤醒词以支持打断功能
     * 3. 语音唤醒检测器具有更好的抗干扰能力
     * 4. 在播放状态下，AEC尤其重要，可以消除设备自身的播放声音
     */
    if (wake_word_detect_.IsDetectionRunning()) {
      if (codec->input_reference() && !ref_data.empty()) {
        // 使用分离的信号进行AEC处理，提高唤醒检测准确性
        wake_word_detect_.Feed(mic_data, ref_data);
      } else {
        // 单声道模式，使用原始数据
        wake_word_detect_.Feed(data);
      }
    }
    // 播放状态下不进行其他音频处理，直接返回
    return;
  }

  // === 正常状态下的音频处理 ===

  // 语音唤醒检测：持续运行，检测用户的唤醒词
  if (wake_word_detect_.IsDetectionRunning()) {
    if (codec->input_reference() && !ref_data.empty()) {
      // 使用分离的信号进行AEC处理，消除环境回声
      wake_word_detect_.Feed(mic_data, ref_data);
    } else {
      // 单声道模式，使用原始数据
      wake_word_detect_.Feed(data);
    }
  }

  // 音频信号处理：仅在监听状态下运行
  if (audio_processor_.IsRunning() &&
      device_state_ == kDeviceStateListening) {
    /**
     * 将音频数据传递给音频处理器
     *
     * 音频处理器会执行以下操作：
     * 1. AEC（Acoustic Echo Cancellation）回声消除
     * 2. NS（Noise Suppression）噪声抑制
     * 3. AGC（Automatic Gain Control）自动增益控制
     * 4. VAD（Voice Activity Detection）语音活动检测
     * 5. 处理后的音频会通过回调传递给OPUS编码器
     */
    audio_processor_.Input(data);
  }

  #else
  /**
   * 非ESP32S3平台的音频处理策略
   *
   * 对于不支持ESP-SR库的平台，直接进行OPUS编码和网络传输。
   * 这种方式缺少本地的语音处理能力，但可以在服务器端进行处理。
   */
  if (device_state_ == kDeviceStateListening) {
    // 将音频编码任务调度到后台线程，避免阻塞音频输入线程
    background_task_->Schedule([this, data = std::move(data)]() mutable {
      // 直接进行OPUS编码，跳过本地音频处理
      opus_encoder_->Encode(std::move(data),
                            [this](std::vector<uint8_t>&& opus) {
                              // 将网络发送任务调度到主线程
                              Schedule([this, opus = std::move(opus)]() {
                                protocol_->SendAudio(opus);
                              });
                            });
    });
  }
  #endif
}

/**
 * @brief 中止当前的语音播放
 *
 * 立即停止当前正在播放的音频内容，清空播放队列，用于处理以下场景：
 * - 用户通过唤醒词打断当前播放
 * - 检测到新的用户语音输入
 * - 网络连接异常需要停止播放
 * - 系统错误或用户手动取消
 *
 * 执行的操作：
 * 1. 设置中止标志，标记当前操作被中断
 * 2. 清空音频解码播放队列，停止所有待播放的音频
 * 3. 记录中止原因，用于调试和统计
 *
 * @param reason 中止原因，用于日志记录和问题诊断
 *               可能的值包括：
 *               - kAbortReasonWakeWordDetected: 检测到唤醒词
 *               - kAbortReasonUserInterrupt: 用户主动中断
 *               - kAbortReasonNetworkError: 网络错误
 *               - kAbortReasonSystemError: 系统错误
 *
 * @note 该函数会立即生效，不等待当前音频帧播放完成
 * @note 调用后设备通常会切换到监听状态，准备接收新的用户输入
 * @thread_safety 线程安全，可在任意线程中调用
 * @performance 快速响应，确保用户交互的流畅性
 *
 * @see audio_decode_queue_->clear() 清空音频播放队列
 * @see aborted_ 中止标志，其他模块可以检查此标志
 */
void Application::AbortSpeaking(AbortReason reason) {
  ESP_LOGI(TAG, "Abort speaking, reason: %d", static_cast<int>(reason));

  // 设置中止标志，其他模块可以检查此标志来判断操作是否被中断
  aborted_ = true;

  // 立即清空音频解码播放队列，停止所有待播放的音频数据
  // 这会中断当前正在播放的音频，并丢弃所有缓存的音频数据
  audio_decode_queue_->clear();

}

/**
 * @brief 设置设备状态
 *
 * 这是设备状态管理的核心函数，负责安全地切换设备状态并执行相应的
 * 初始化和清理操作。状态转换会触发相关硬件和软件模块的重新配置。
 *
 * 支持的设备状态：
 * - kDeviceStateUnknown: 未知状态
 * - kDeviceStateStarting: 启动中
 * - kDeviceStateWifiConfiguring: WiFi配置中
 * - kDeviceStateIdle: 空闲状态
 * - kDeviceStateConnecting: 连接中
 * - kDeviceStateListening: 监听中（等待语音输入）
 * - kDeviceStateSpeaking: 播放中（输出音频）
 * - kDeviceStateUpgrading: 升级中
 * - kDeviceStateMoaning: 本地音频播放中
 * - kDeviceStateOffline: 离线状态
 *
 * @param state 目标设备状态
 *
 * @note 状态转换包含保护机制，升级状态下不允许切换到其他状态
 * @note 从播放状态切换到空闲状态时会等待音频播放完成
 * @thread_safety 线程安全，应该只在主线程中调用（通过Schedule()调度）
 * @see DeviceState 设备状态枚举定义
 */
void Application::SetDeviceState(DeviceState state) {
  // === 状态转换保护机制 ===

  /**
   * 升级状态保护：防止升级过程被中断
   *
   * 当设备处于升级状态时，除了升级状态本身，不允许切换到任何其他状态。
   * 这确保了OTA升级过程的完整性和安全性。
   */
  if (device_state_ == kDeviceStateUpgrading && state != kDeviceStateUpgrading) {
    ESP_LOGW(TAG, "Device is upgrading, ignoring state change request to %s",
             STATE_STRINGS[state]);
    return;
  }

  // 如果目标状态与当前状态相同，无需处理
  if (device_state_ == state) {
    return;
  }

  // 记录状态转换日志，便于调试和监控
  ESP_LOGW(TAG, "STATE: %s -> %s", STATE_STRINGS[device_state_], STATE_STRINGS[state]);

  // === 特殊状态转换处理 ===

  /**
   * 播放状态到空闲状态的特殊处理
   *
   * 当从播放状态切换到空闲状态时，需要确保所有音频数据都已播放完成，
   * 避免音频截断影响用户体验。
   */
  if (device_state_ == kDeviceStateSpeaking && state == kDeviceStateIdle) {
    // 检查音频解码队列是否还有未播放的数据
    if (!audio_decode_queue_->empty()) {
      return;  // 还有数据未播放完，暂不切换状态
    }
  }

  // === 执行状态切换 ===

  // 更新设备状态变量
  device_state_ = state;

  // 等待所有后台任务完成，确保状态切换的原子性
  // 这防止了状态切换过程中的竞态条件
  background_task_->WaitForCompletion();

  // === 硬件状态更新 ===

  // 更新LED指示灯状态，反映当前设备状态
  auto led = Board::GetInstance().GetLed();
  led->OnStateChanged();

  // === 离线模式标志管理（条件编译） ===

  #if LOCAL_MOAN_PLAYING == 1
  // 根据设备状态设置离线标志，用于本地音频播放功能
  if (state == kDeviceStateOffline) {
      OFFLINE_FLAG = true;   // 进入离线模式
  } else {
      OFFLINE_FLAG = false;  // 退出离线模式
  }
  #endif

  switch (state) {
    case kDeviceStateUnknown:
    case kDeviceStateIdle:
      // display->SetStatus("待命");
      // display->SetEmotion("neutral");
      ResetDecoder();
      opus_encoder_->ResetState();
#ifdef CONFIG_IDF_TARGET_ESP32S3
      audio_processor_.Stop();
#endif
      break;
    case kDeviceStateConnecting:
      // display->SetStatus("连接中...");
      break;
    case kDeviceStateListening:{


#if CONFIG_IDF_TARGET_ESP32S3
      audio_processor_.Start();
#endif
      ResetDecoder();
      // cancel_tts_sent_ = false; // 重置标志，允许在新的listening周期发送CancelTTS
      UpdateIotStates();
      break;
    }
    case kDeviceStateSpeaking:
      // display->SetStatus("说话中...");
      ResetDecoder();
      // opus_encoder_->ResetState();
#if CONFIG_IDF_TARGET_ESP32S3
      // 不停止音频处理器
      audio_processor_.Stop();  // 删除或注释掉

      // 确保唤醒词检测也在运行
      if (!wake_word_detect_.IsDetectionRunning()) {
        wake_word_detect_.StartDetection();
      }
#endif
      break;
    case kDeviceStateUpgrading:
      // 处理升级状态 - 确保所有音频处理都被禁用
      {
        auto codec = Board::GetInstance().GetAudioCodec();
        codec->EnableOutput(false);
        codec->EnableInput(false);

        // 清空音频缓冲区
        // std::lock_guard<std::mutex> lock(mutex_);
        audio_decode_queue_->clear();

      }
#if CONFIG_IDF_TARGET_ESP32S3
      
      audio_processor_.Stop();
      wake_word_detect_.StopDetection();
      audio_decode_queue_->Stop();
#endif
      break;

#if LOCAL_MOAN_PLAYING == 1 
    case kDeviceStateOffline: {
    #if 1
        auto codec = Board::GetInstance().GetAudioCodec();
        // 只关闭输入，保留输出
        codec->EnableInput(false);
        if (audio_decode_queue_) {
            audio_decode_queue_->clear();
        }
        #if CONFIG_IDF_TARGET_ESP32S3
        audio_processor_.Stop();
        wake_word_detect_.StopDetection();
        #endif
        
        break;

    #else
        auto codec = Board::GetInstance().GetAudioCodec();
        codec->EnableOutput(false);
        codec->EnableInput(false);

        if (audio_decode_queue_) {
            audio_decode_queue_->clear();
        }

        #if CONFIG_IDF_TARGET_ESP32S3
        audio_processor_.Stop();
        wake_word_detect_.StopDetection();
        #endif
        break;
    #endif
    }
#endif

    default:
      // Do nothing
      break;
  }
}

/**
 * @brief 设置音频解码采样率
 *
 * 根据服务器发送的音频数据采样率，动态配置OPUS解码器和输出重采样器。
 * 这允许系统适应不同采样率的音频流，确保音频播放的兼容性。
 *
 * 主要功能：
 * 1. 检查采样率是否需要更新
 * 2. 重新创建OPUS解码器实例
 * 3. 配置输出重采样器（如果需要）
 * 4. 确保音频输出的采样率匹配
 *
 * 采样率处理策略：
 * - 服务器音频 → OPUS解码 → 重采样（如需要） → 音频编解码器输出
 * - 常见采样率：8kHz、16kHz、24kHz、48kHz
 * - 目标采样率：音频编解码器的输出采样率
 *
 * @param sample_rate 目标解码采样率（Hz）
 *
 * @note 当前实现已被简化，采样率配置由AudioDecodeQueue内部管理
 * @note 保留此函数接口用于兼容性和未来扩展
 * @thread_safety 线程安全，应在主线程中调用
 *
 * @see AudioDecodeQueue 内部管理OPUS解码器和重采样
 * @see OpusResampler 音频重采样器实现
 */
void Application::SetDecodeSampleRate(int sample_rate) {
  // === 历史实现（已被注释，保留用于参考） ===

  /**
   * 以下代码展示了之前的完整实现逻辑：
   *
   * 1. 检查采样率是否已经设置为目标值
   * 2. 创建新的OPUS解码器实例
   * 3. 配置输出重采样器处理采样率转换
   *
   * 现在这些功能都由AudioDecodeQueue统一管理，提供更好的封装性。
   */

  // 检查是否需要更新采样率
  // if (opus_decode_sample_rate_ == sample_rate) {
  //   return;  // 采样率未变化，无需重新配置
  // }

  // 更新解码器采样率并重新创建解码器实例
  // opus_decode_sample_rate_ = sample_rate;
  // opus_decoder_ = std::make_unique<OpusDecoderWrapper>(opus_decode_sample_rate_, 1);

  // 配置输出重采样器（如果解码采样率与输出采样率不匹配）
  // auto codec = Board::GetInstance().GetAudioCodec();
  // if (opus_decode_sample_rate_ != codec->output_sample_rate()) {
  //   ESP_LOGI(TAG, "Resampling audio from %d to %d", opus_decode_sample_rate_,
  //            codec->output_sample_rate());
  //   output_resampler_.Configure(opus_decode_sample_rate_,
  //                               codec->output_sample_rate());
  // }

  // === 当前实现 ===

  /**
   * 当前版本中，采样率配置由AudioDecodeQueue内部处理：
   * - AudioDecodeQueue会根据接收到的音频数据自动检测采样率
   * - 内部的OPUS解码器会自动适应不同的采样率
   * - 重采样操作在AudioDecodeQueue内部完成
   *
   * 这种设计提供了更好的封装性和自动化程度。
   */

  // 当前实现为空，功能由AudioDecodeQueue内部处理
  (void)sample_rate;  // 避免未使用参数警告
}

void Application::UpdateIotStates() {
  auto& thing_manager = iot::ThingManager::GetInstance();
  auto states = thing_manager.GetStatesJson();
  if (states != last_iot_states_) {
    last_iot_states_ = states;
    protocol_->SendIotStates(states);
  }
}

void Application::CloseAudioChannel() {
  if (protocol_ != nullptr) {
    protocol_->CloseAudioChannel();
  }

  // 发送完音频后，保持在 listening 状态，等待服务器响应
  // 不要切换到 idle 状态
  // SetDeviceState(kDeviceStateIdle);  // 删除这行

  // 添加超时检查
  background_task_->Schedule([this]() {
    vTaskDelay(pdMS_TO_TICKS(15000));  //  用于避免TTT逻辑延误
    std::lock_guard<std::mutex> lock(mutex_);
    if (device_state_ == kDeviceStateListening) {
      ESP_LOGW(TAG,
               "No response from server after 2 seconds, switching to idle");
      SetDeviceState(kDeviceStateIdle);
    }
  });
}

void Application::SaveLanguageTypeToNVS(const std::string& language) {
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open("config", NVS_READWRITE, &nvs_handle);
  if (err == ESP_OK) {
    err = nvs_set_str(nvs_handle, "languagesType", language.c_str());
    if (err != ESP_OK) {
      ESP_LOGE(TAG, "Failed to save languagesType to NVS");
    } else {
      ESP_LOGI(TAG, "languagesType saved to NVS: %s", language.c_str());
    }
    nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
  } else {
    ESP_LOGE(TAG, "Failed to open NVS for writing");
  }
}

void Application::SaveVolumeToNVS(int volume) {
  nvs_handle_t nvs_handle;
  if (nvs_open("config", NVS_READWRITE, &nvs_handle) == ESP_OK) {
    if (nvs_set_i32(nvs_handle, "volume", volume) != ESP_OK) {
      ESP_LOGW(TAG, "Failed to save volume to NVS");
    } else {
      ESP_LOGI(TAG, "Volume saved to NVS: %d", volume);
    }
    nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
  } else {
    ESP_LOGE(TAG, "Failed to open NVS for writing volume setting");
  }
}

int Application::LoadVolumeFromNVS(int default_value) {
  int volume = default_value;
  nvs_handle_t nvs_handle;

  if (nvs_open("config", NVS_READONLY, &nvs_handle) == ESP_OK) {
    int32_t saved_volume;  // Use int32_t to match nvs_get_i32 expectations
    if (nvs_get_i32(nvs_handle, "volume", &saved_volume) == ESP_OK) {
      // Ensure it's in valid range
      if (saved_volume >= 0 && saved_volume <= 100) {
        volume = saved_volume;
        ESP_LOGI(TAG, "Loaded saved volume: %d", volume);
      }
    }
    nvs_close(nvs_handle);
  }

  return volume;
}

// 从NVS获取当前语言设置，如果不存在则返回默认值
std::string Application::GetLanguageFromNVS() {
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open("storage", NVS_READONLY, &nvs_handle);
  if (err != ESP_OK) {
    ESP_LOGE(TAG, "Error opening NVS: %s", esp_err_to_name(err));
    return "en";  // 默认返回英语
  }

  // 首先获取需要的缓冲区大小
  size_t required_size = 0;
  err = nvs_get_str(nvs_handle, "language", nullptr, &required_size);
  if (err != ESP_OK && err != ESP_ERR_NVS_NOT_FOUND) {
    ESP_LOGE(TAG, "Error getting language size: %s", esp_err_to_name(err));
    nvs_close(nvs_handle);
    return "en";
  }

  // 如果找不到键，返回默认值
  if (err == ESP_ERR_NVS_NOT_FOUND) {
    nvs_close(nvs_handle);
    return "en";
  }

  // 分配缓冲区并读取值
  char* buffer = new char[required_size];
  err = nvs_get_str(nvs_handle, "language", buffer, &required_size);
  if (err != ESP_OK) {
    ESP_LOGE(TAG, "Error getting language: %s", esp_err_to_name(err));
    delete[] buffer;
    nvs_close(nvs_handle);
    return "en";
  }

  std::string language(buffer);
  delete[] buffer;
  nvs_close(nvs_handle);

  return language;
}

/**
 * @brief 监控音频停止标志和音频队列状态
 *
 * 该函数运行在独立的低优先级线程中，负责监控音频播放的结束条件：
 * - 监控MQTT停止标志（mqtt_stop_flag_）
 * - 检查音频解码队列是否为空
 * - 确认是否有新的音频数据到达
 * - 在满足条件时自动切换设备状态
 *
 * 状态切换条件：
 * 1. 设备处于播放状态（kDeviceStateSpeaking）
 * 2. MQTT停止标志被设置（服务器发送停止指令）
 * 3. 音频解码队列为空（所有音频数据已播放）
 * 4. 超过280ms没有接收到新的音频数据
 *
 * 线程特性：
 * - 线程名称: "monitor_stop_audio"
 * - 堆栈大小: 4KB
 * - 优先级: 1（最低优先级，后台监控任务）
 * - 检查间隔: 100ms
 *
 * @note 该函数包含无限循环，应在独立线程中运行
 * @note 280ms的延迟是为了避免网络抖动导致的误切换
 * @thread_safety 线程安全，使用Schedule()与主线程通信
 */
void Application::MonitorStopAndAudioQueue() {
    while (true) {
        // 每100ms检查一次，平衡响应性和CPU使用率
        vTaskDelay(pdMS_TO_TICKS(100));

        // === 检查音频停止条件 ===

        /**
         * 同时满足以下条件时才处理停止逻辑：
         * 1. mqtt_stop_flag_: 服务器发送了停止播放指令
         * 2. device_state_ == kDeviceStateSpeaking: 设备当前处于播放状态
         */
        if ((mqtt_stop_flag_) && (device_state_ == kDeviceStateSpeaking)) {
            // 检查音频解码队列是否已清空
            if (audio_decode_queue_->empty()) {
                // === 计算距离上次接收音频数据的时间 ===

                auto now = std::chrono::steady_clock::now();
                auto ms_since_last_audio = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - last_audio_received_time_).count();

                /**
                 * 280ms防抖延迟
                 *
                 * 只有在280ms内没有接收到新音频数据时才切换状态。
                 * 这个延迟是为了处理以下情况：
                 * - 网络延迟导致的音频数据间隔
                 * - 服务器分批发送音频数据
                 * - 避免因短暂的网络抖动导致误切换
                 */
                if (ms_since_last_audio > 280) {
                    ESP_LOGI(TAG, "Audio queue empty and no new audio for 280ms, switching to listening");

                    // 重置停止标志
                    mqtt_stop_flag_ = false;

                    // === 调度状态切换到主线程 ===

                    Schedule([this]() {
                        // 再次清空音频队列，确保没有残留数据
                        audio_decode_queue_->clear();

                        // 延时650ms，给音频硬件足够的时间完成当前播放
                        vTaskDelay(pdMS_TO_TICKS(650));

                        // 切换到监听状态，准备接收新的用户指令
                        SetDeviceState(kDeviceStateListening);

                        // 设置语音检测标志，允许立即检测用户语音
                        voice_detected_ = true;

                        // 设置静音计数，给用户足够的反应时间
                        silence_count_ = 10;
                    });
                }
            }
        }
    }
}


#if WIFI_SIGNAL_CHECK_TONE == 1
/**
 * @brief 监控WiFi信号强度（条件编译）
 *
 * 该函数运行在独立线程中，持续监控WiFi信号强度（RSSI），
 * 当信号强度持续较弱时触发相应的处理逻辑。
 *
 * 监控逻辑：
 * - 每3秒检查一次WiFi信号强度
 * - RSSI阈值：-70dBm（低于此值认为信号较弱）
 * - 连续3次检测到弱信号才触发处理
 * - 信号恢复后重置计数器
 *
 * 信号强度参考：
 * - RSSI > -50dBm: 信号很强
 * - -50dBm ~ -70dBm: 信号良好
 * - -70dBm ~ -85dBm: 信号较弱
 * - RSSI < -85dBm: 信号很弱
 *
 * 线程特性：
 * - 线程名称: "monitor_wifi_rssi"
 * - 堆栈大小: 4KB
 * - 优先级: 1（低优先级后台任务）
 * - 检查间隔: 3秒
 *
 * @note 该函数包含无限循环，应在独立线程中运行
 * @note 只有在WIFI_SIGNAL_CHECK_TONE宏定义为1时才编译此函数
 * @thread_safety 线程安全，使用Schedule()与主线程通信
 * @hardware_requirement 需要WiFi模块处于STA模式并已连接到AP
 */
void Application::MonitorWifiRssi() {
    // 弱信号计数器，用于防抖和确认信号确实较弱
    uint8_t weak_count = 0;

    while (true) {
        // 每3秒检查一次WiFi信号强度，避免过于频繁的检查
        vTaskDelay(pdMS_TO_TICKS(3000));

        // === 获取WiFi状态信息 ===

        wifi_mode_t mode;
        esp_wifi_get_mode(&mode);  // 获取当前WiFi工作模式

        wifi_ap_record_t ap_info;  // AP信息结构体
        int8_t rssi = -127;        // 初始化为最弱信号值

        // === 检查WiFi连接状态和信号强度 ===

        /**
         * 只有在以下条件同时满足时才进行信号强度检查：
         * 1. WiFi处于STA（Station）模式
         * 2. 成功连接到AP（Access Point）
         */
        if ((mode == WIFI_MODE_STA) && (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK)) {
            // 获取当前连接AP的信号强度
            if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
                rssi = ap_info.rssi;
            }

            // === 信号强度评估 ===

            /**
             * 信号强度阈值判断：-70dBm
             *
             * 如果信号强度高于-70dBm，认为信号良好：
             * - 重置弱信号计数器
             * - 继续下一次检查
             */
            if (rssi > -70) {
                weak_count = 0;  // 信号良好，重置计数器
                continue;
            }

            // === 弱信号处理 ===

            // 检测到弱信号，增加计数器
            weak_count++;

            /**
             * 连续弱信号确认机制
             *
             * 只有连续3次（9秒）检测到弱信号才触发处理，
             * 这避免了因瞬时信号波动导致的误报。
             */
            if (weak_count >= 3) {
                weak_count = 0;  // 重置计数器

                // 调度弱信号处理到主线程
                Schedule([this]() {
                    HandleWifiWeak();
                });
            }
        } else {
            // WiFi未连接或不在STA模式，暂不处理
            // 可以在这里添加WiFi断开的处理逻辑
        }
    }
}

  void Application::HandleWifiWeak() {
    auto prev_state = device_state_;

      if (device_state_ == kDeviceStateSpeaking) {

          AbortSpeaking(kAbortReasonNone);
          protocol_->SendCancelTTS();
          audio_decode_queue_->clear();
      }


    SetDeviceState(kDeviceStateSpeaking);
    audio_decode_queue_->clear(); // 再次确保队列清空

    wifi_ap_record_t ap_info;
    bool wifi_connected = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);

    if (wifi_connected) {
        audio_decode_queue_->clear();
        // PlayOpusFile(p3_dingdingwifi_start, p3_dingdingwifi_end - p3_dingdingwifi_start);
        // vTaskDelay(pdMS_TO_TICKS(1000));
        PlayOpusFile(p3_wifi_weak_start, p3_wifi_weak_end - p3_wifi_weak_start);
        vTaskDelay(pdMS_TO_TICKS(2500));

    } 
        if (prev_state == kDeviceStateIdle) {
            SetDeviceState(kDeviceStateIdle); 
        } else {
            mqtt_stop_flag_ = false;
            SetDeviceState(kDeviceStateListening); 

            voice_detected_ = true;
            silence_count_ = 10;
        }
      
    
  }


#endif
