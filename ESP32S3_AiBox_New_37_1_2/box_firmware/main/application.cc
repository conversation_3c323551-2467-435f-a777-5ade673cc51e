#include "application.h"

#include <arpa/inet.h>
#include <cJSON.h>
#include <driver/gpio.h>
#include <esp_log.h>
#include "esp_timer.h"
#include <cmath>
#include <cstring>
#include <random>
#include "audio_codec.h"
#include "board.h"
#include "boards/common/button.h"
#include "display.h"
#include "esp32_s3_szp.h"
#include "esp_sleep.h"
#include "font_awesome_symbols.h"
#include "iot/thing_manager.h"
#include "led/single_led.h"
#include "ml307_ssl_transport.h"
#include "mqtt_protocol.h"
#include "system_info.h"
#include "websocket_protocol.h"
#include "../local_moaning_data.h"

#include "boards/lichuang-dev/uart_433.h"
#include "project_config.h"
#include "esp_wifi.h"
#include "Local_moan.h"

#define TAG "Application"

#if FF_IMU_PARAM_IS_Wire == 0
static int last_button_int = 0;  
static bool first_key = true;     
#endif

#if LOCAL_MOAN_PLAYING == 1
static uint8_t moan_count = 1;
static bool local_moan_is_playing = false;
static bool IMU_CHANGE_FLAG = false;
#endif


#if WIFI_SIGNAL_CHECK_TONE == 1
static bool mqtt_first_connect_flag = false; 
#endif

#if SEND_MAC_ADDRESS_433 == 1
bool OTA_UPGRADE_FLAG = false;
#endif


#if WIFI_SIGNAL_CHECK_TONE == 1
extern const char p3_wifi_weak_start[] asm("_binary_wifi_weak_p3_start");
extern const char p3_wifi_weak_end[]   asm("_binary_wifi_weak_p3_end");
#endif


#if WIFI_CONNECT_CHECK_TONE == 1
extern const char p3_NETWORKdisc_start[] asm("_binary_NETWORKdisc_p3_start");
extern const char p3_NETWORKdisc_end[]   asm("_binary_NETWORKdisc_p3_end");
#endif



//
extern const char p3_zh_start[] asm("_binary_linkwifi_zh_p3_start");
extern const char p3_zh_end[] asm("_binary_linkwifi_zh_p3_end");
//
extern const char p3_en_start[] asm("_binary_linkwifi_en_p3_start");
extern const char p3_en_end[] asm("_binary_linkwifi_en_p3_end");
//
extern const char p3_ja_start[] asm("_binary_linkwifi_ja_p3_start");
extern const char p3_ja_end[] asm("_binary_linkwifi_ja_p3_end");


extern const char p3_dong_start[] asm("_binary_dong_p3_start");
extern const char p3_dong_end[] asm("_binary_dong_p3_end");
extern const char p3_update_start[] asm("_binary_update_p3_start");
extern const char p3_update_end[] asm("_binary_update_p3_end");
//
MoaningString(err_pin);
MoaningString(err_reg);
//

static const char* const STATE_STRINGS[] = {
    "unknown",   "starting", "configuring", "idle",        "connecting",
    "listening", "speaking", "upgrading","moaning" ,"offline","invalid_state"};


Application::Application() {
  event_group_ = xEventGroupCreate();
  background_task_ = new BackgroundTask(4096 * 8);

  ////初始化OTA相关参数
  ota_.SetCheckVersionUrl(CONFIG_OTA_VERSION_URL);
  ota_.SetHeader("Device-Id", SystemInfo::GetMacAddress().c_str());

  opus_decode_sample_rate_ = 16000;  // 设置为服务器发送的PCM数据的采样率

  // 初始化音频缓冲区
  for (auto& buffer : audio_buffers_) {
    buffer.reserve(4096);  // 预分配合适的大小
  }
  last_output_time_ = std::chrono::steady_clock::now();

  current_volume_ = 80;  // Initialize with default volume
}

Application::~Application() {
  if (background_task_ != nullptr) {
    delete background_task_;
  }
  vEventGroupDelete(event_group_);
}

void Application::CheckNewVersion() {
  auto& board = Board::GetInstance();

  auto display = board.GetDisplay();
  // Check if there is a new firmware version available
  ota_.SetPostData(board.GetJson());
//   return ;
  while (true) {
    if (ota_.CheckVersion()) {
      if (ota_.HasNewVersion()) {

        #if SEND_MAC_ADDRESS_433 == 1
        OTA_UPGRADE_FLAG = true;
        #endif

        #if BOOT_BEGAIN_OTA == 0
        // Wait for the chat state to be idle
        do {
          vTaskDelay(pdMS_TO_TICKS(3000));
        } while (GetDeviceState() != kDeviceStateIdle);

        #endif

        // Use main task to do the upgrade, not cancelable
        Schedule([this, &board, display]() {
          SetDeviceState(kDeviceStateUpgrading);

          // if(display){
              display->SetIcon(FONT_AWESOME_DOWNLOAD);
              display->SetStatus("新版本 " + ota_.GetFirmwareVersion());
          // }

          auto codec = board.GetAudioCodec();
          codec->EnableOutput(true);
          // 播放升级音频提示
          PlayOpusFile(p3_update_start, p3_update_end - p3_update_start);
          ESP_LOGE(TAG, "Firmware upgrade..."); 
          vTaskDelay(pdMS_TO_TICKS(2500));

          // 预先关闭音频输出和输入，避免升级过程有音频操作
          
          codec->EnableInput(false);  // 关闭麦克风输入

          // 清空音频队列
          {
            audio_decode_queue_->clear();
            
          }

          // 停止正在进行的背景任务
          background_task_->WaitForCompletion();

          // 停止音频处理器和唤醒词检测
#if CONFIG_IDF_TARGET_ESP32S3
          audio_processor_.Stop();
          wake_word_detect_.StopDetection();
#endif

          // 等待任何正在进行的音频操作完成
          vTaskDelay(pdMS_TO_TICKS(1000));

          // 开始升级
          ota_.StartUpgrade([this,display](int progress, size_t speed) {
            
            char buffer[64];
            snprintf(buffer, sizeof(buffer), "%d%% %zuKB/s", progress,
                     speed / 1024);
            if (oudio_output_finish_) {
              PlayOpusFile(p3_update_start, p3_update_end - p3_update_start);
              oudio_output_finish_ = false;
            }
            // display->SetStatus(buffer);
          });
          // 如果升级成功，设备会重启，不会执行到这里
          // 如果执行到这里，说明升级失败
          ESP_LOGE(TAG, "Firmware upgrade failed...");
          display->SetStatus("升级失败");
          vTaskDelay(pdMS_TO_TICKS(3000));

          // 重启设备
          esp_restart();
        });
      } else {
        ota_.MarkCurrentVersionValid();
        // display->ShowNotification("版本 " + ota_.GetCurrentVersion());
      }
      return;
    }

    // Check again in 60 seconds
    vTaskDelay(pdMS_TO_TICKS(60000));
  }
}

void Application::Alert(const std::string& title, const std::string& message) {
  ESP_LOGW(TAG, "Alert: %s, %s", title.c_str(), message.c_str());

  if (message == "Configuring WiFi" || message == "Registration denied") {
    // 从NVS获取当前语言设置
    std::string language = GetLanguageFromNVS();
    ESP_LOGI(TAG, "Current language setting: %s", language.c_str());

    // PlayOpusFile(p3_ja_start, p3_ja_end - p3_ja_start);
    PlayOpusFile(p3_en_start, p3_en_end - p3_en_start);
    // PlayOpusFile(p3_zh_start, p3_zh_end - p3_zh_start);
    // 根据语言选择对应的音频文件
    // if (language == "zh") {
    //   PlayOpusFile(p3_zh_start, p3_zh_end - p3_zh_start);
    // } else if (language == "en") {
    //   PlayOpusFile(p3_en_start, p3_en_end - p3_en_start);
    // // } else if (language == "ko") {
    // //   PlayOpusFile(p3_ko_start, p3_ko_end - p3_ko_start);
    // // } else if (language == "ja") {
    // //   PlayOpusFile(p3_ja_start, p3_ja_end - p3_ja_start);
    // // } else if (language == "de") {
    // //   PlayOpusFile(p3_de_start, p3_de_end - p3_de_start);
    // // } else if (language == "fr") {
    // //   PlayOpusFile(p3_fr_start, p3_fr_end - p3_fr_start);
    // // } else if (language == "es") {
    // //   PlayOpusFile(p3_es_start, p3_es_end - p3_es_start);
    // // } else if (language == "pt") {
    // //   PlayOpusFile(p3_pt_start, p3_pt_end - p3_pt_start);
    // } else {
    //   // 默认使用英语，如果没有找到匹配的语言
    //   // PlayOpusFile(p3_zh_start, p3_zh_end - p3_zh_start);
    //   ESP_LOGW(TAG, "Unknown language: %s, using English as default",
    //            language.c_str());
    // //   PlayOpusFile(p3_en_start, p3_en_end - p3_en_start);
    // }
  }
}

void Application::PlayOpusFile(const char* data, size_t size) {
  audio_decode_queue_->PlayLocalFile(data, size);
  //   ESP_LOGW(TAG, "PlayOpusFile: %zu bytes", size);

  //   // 检查是否为Ogg封装格式 (头部是'OggS')
  //   if (size >= 4 && data[0] == 'O' && data[1] == 'g' && data[2] == 'g' &&
  //       data[3] == 'S') {
  //     ESP_LOGI(TAG, "Detected Ogg container format, extracting Opus
  //     packets...");

  //     // 为了简单，我们将Ogg文件视为一系列数据包
  //     // 实际上，这需要更复杂的Ogg解析
  //     const char* p = data;
  //     bool found_opus_header = false;

  //     // 搜索"OpusHead"标记，确认为Opus数据
  //     for (size_t i = 0; i < size - 8; i++) {
  //       if (memcmp(data + i, "OpusHead", 8) == 0) {
  //         found_opus_header = true;
  //         ESP_LOGI(TAG, "Found OpusHead marker at offset %d", i);
  //         break;
  //       }
  //     }

  //     if (!found_opus_header) {
  //       ESP_LOGE(TAG, "Ogg file does not contain Opus data");
  //       return;
  //     }

  //     // 设置为播放模式
  //     SetDeviceState(kDeviceStateSpeaking);
  //     SetDecodeSampleRate(16000);

  //     // 直接将整个Ogg文件发送到解码器
  //     // 这依赖于解码器能够处理Ogg容器
  //     std::vector<uint8_t> opus(data, data + size);
  //     // std::lock_guard<std::mutex> lock(mutex_);
  //     audio_decode_queue_->push_back(std::move(opus));

  //     // 确保音频输出设备已启用
  //     auto codec = Board::GetInstance().GetAudioCodec();
  //     codec->EnableOutput(true);

  //     // 触发音频输出事件
  //     xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
  //     return;
  //   }

  //   // 如果不是Ogg格式，尝试使用BinaryProtocol3格式解析
  //   ESP_LOGI(TAG, "Trying to parse as BinaryProtocol3 format...");
  //   PlayLocalFile(data, size);
}


#if WIFI_CONNECT_CHECK_TONE == 1
void Application::PlayNetworkDisc() {
    PlayOpusFile(reinterpret_cast<const char*>(p3_NETWORKdisc_start),
                 p3_NETWORKdisc_end - p3_NETWORKdisc_start);
}
#endif
// void Application::PlayLocalFile(const char* data, size_t size) {
//   ESP_LOGI(TAG, "PlayLocalFile: %zu bytes", size);
//   SetDecodeSampleRate(16000);
//   for (const char* p = data; p < data + size;) {
//     auto p3 = (BinaryProtocol3*)p;
//     p += sizeof(BinaryProtocol3);

//     auto payload_size = ntohs(p3->payload_size);
//     std::vector<uint8_t> opus;
//     opus.resize(payload_size);
//     memcpy(opus.data(), p3->payload, payload_size);
//     p += payload_size;

//     // std::lock_guard<std::mutex> lock(mutex_);
//     audio_decode_queue_->emplace_back(std::move(opus));
//   }
// }

void Application::ToggleChatState() {
  Schedule([this]() {
    if (!protocol_) {
      ESP_LOGE(TAG, "Protocol not initialized");
      return;
    }

    if (device_state_ == kDeviceStateIdle) {
      SetDeviceState(kDeviceStateConnecting);
      if (!protocol_->OpenAudioChannel()) {
        Alert("Error", "Failed to open audio channel");
        SetDeviceState(kDeviceStateIdle);
        return;
      }

      keep_listening_ = true;

      SetDeviceState(kDeviceStateListening);
    } else if (device_state_ == kDeviceStateSpeaking) {
      AbortSpeaking(kAbortReasonNone);

    } else if (device_state_ == kDeviceStateListening) {
      protocol_->CloseAudioChannel();
    }
  });
}

void Application::StartListening() {
  Schedule([this]() {
    if (!protocol_) {
      ESP_LOGE(TAG, "Protocol not initialized");
      return;
    }

    keep_listening_ = false;
    if (device_state_ == kDeviceStateIdle) {
      if (!protocol_->IsAudioChannelOpened()) {
        SetDeviceState(kDeviceStateConnecting);
        if (!protocol_->OpenAudioChannel()) {
          SetDeviceState(kDeviceStateIdle);
          Alert("Error", "Failed to open audio channel");
          return;
        }
      }
      SetDeviceState(kDeviceStateListening);
    } else if (device_state_ == kDeviceStateSpeaking) {
      AbortSpeaking(kAbortReasonNone);
      // FIXME: Wait for the speaker to empty the buffer
      vTaskDelay(pdMS_TO_TICKS(120));
      SetDeviceState(kDeviceStateListening);
    }
  });
}
t_sQMI8658 QMI8658;  // 定义QMI8658结构体变量
void Application::StopListening() {
  Schedule([this]() {
    if (device_state_ == kDeviceStateListening) {
      protocol_->SendStopListening();
      ESP_LOGI(TAG, "StopListeningo ");
      SetDeviceState(kDeviceStateIdle);
    }
  });
}

void Application::Start() {

  auto& board = Board::GetInstance();
  //
  //

  //
  qmi8658_init(board.GetI2cDevice());
  SetDeviceState(kDeviceStateStarting);
  // qmi8658_fetch_angleFromAcc(&QMI8658);  // 获取XYZ轴的倾角

  /* Setup the display */
  // auto display = board.GetDisplay();

  /* Setup the audio codec */
  auto codec = board.GetAudioCodec();
  ESP_LOGI(TAG, "Codec configuration: input_rate=%d, output_rate=%d",
           codec->input_sample_rate(), codec->output_sample_rate());

  // Load volume from NVS or use default
  int default_volume = 80;
  current_volume_ = LoadVolumeFromNVS(default_volume);
  codec->SetOutputVolume(current_volume_);
  ESP_LOGI(TAG, "Set initial volume to %d", current_volume_);

//   opus_decode_sample_rate_ = 16000;  // 16000
//   opus_decoder_ =
//       std::make_unique<OpusDecoderWrapper>(opus_decode_sample_rate_, 1);
  opus_encoder_ =
      std::make_unique<OpusEncoderWrapper>(16000, 1, OPUS_FRAME_DURATION_MS);
  if (codec->input_sample_rate() != 16000) {
    input_resampler_.Configure(codec->input_sample_rate(), 16000);
    reference_resampler_.Configure(codec->input_sample_rate(), 16000);
  }
  codec->OnInputReady([this, codec]() {
    BaseType_t higher_priority_task_woken = pdFALSE;
    xEventGroupSetBitsFromISR(event_group_, AUDIO_INPUT_READY_EVENT,
                              &higher_priority_task_woken);
    return higher_priority_task_woken == pdTRUE;
  });
  codec->OnOutputReady([this]() {
    BaseType_t higher_priority_task_woken = pdFALSE;
    // xEventGroupSetBitsFromISR(event_group_, AUDIO_OUTPUT_READY_EVENT,
    //                           &higher_priority_task_woken);
    return higher_priority_task_woken == pdTRUE;
  });
  codec->Start();


  #if 1
  audio_decode_queue_ =
      std::make_unique<AudioDecodeQueue>(board.GetAudioCodec(), [this]() -> bool {
        oudio_output_finish_ = true;
        return false;  

      });


  #else
  audio_decode_queue_ =
      std::make_unique<AudioDecodeQueue>(board.GetAudioCodec(), [this]() -> bool {
        oudio_output_finish_ = true;
        ESP_LOGI("[output]", "**************** output02 value: %d*****************",oudio_output_finish_); 
   
          if (stop_after_playback_) {
            stop_after_playback_ = false;
                 Schedule([this]() {
            SetDeviceState(kDeviceStateListening);
            voice_detected_ = true;
            silence_count_=10;
            }); 
                  return true; 
          }
          return false;   

      });
  #endif


  /* Start the main loop */
  xTaskCreate(
      [](void* arg) {
        Application* app = (Application*)arg;
        app->MainLoop();
        vTaskDelete(NULL);
      },
      "main_loop", 4096, this, 2, nullptr); 
  /* Start the main loop */

  xTaskCreate(
      [](void* arg) {
        Application* app = (Application*)arg;
        app->ImuRead();
        vTaskDelete(NULL);
      },
      "imu_read", 4024 , this, 2, nullptr);

  /* Start the input audio processing thread */
  xTaskCreate(
      [](void* arg) {
        Application* app = (Application*)arg;
        app->InputAudioLoop();
        vTaskDelete(NULL);
      },                                            
      "input_audio", 8192, this, 5, nullptr);  

  #if 1
  xTaskCreate(
    [](void* arg) {
      Application* app = (Application*)arg;
      app->MonitorStopAndAudioQueue();
      
    },
    "monitor_stop_audio", 4096, this, 1, nullptr);
  #endif

#if WIFI_SIGNAL_CHECK_TONE == 1
 
  xTaskCreate(
    [](void* arg) {
        Application* app = (Application*)arg;
        app->MonitorWifiRssi();
        vTaskDelete(NULL);
    },
    "monitor_wifi_rssi", 4096, this, 1, nullptr);
#endif

  /* Wait for the network to be ready */
  board.StartNetwork();
  // Check for new firmware version or get the MQTT broker address
  xTaskCreate(
      [](void* arg) {
        Application* app = (Application*)arg;
        app->CheckNewVersion();
        vTaskDelete(NULL);
      },
      "check_new_version", 6800, this, 1, nullptr);



#if 0
  xTaskCreate(
    [](void* arg) {
        Application* app = (Application*)arg;
        while (true) {
            vTaskDelay(pdMS_TO_TICKS(3000));
            // 只在MQTT已连接时自愈
            if (app->protocol_ && app->protocol_->IsMqttConnected()) {
                app->Schedule([app]() {
                    if (app->device_state_ == kDeviceStateListening && !app->protocol_->IsAudioChannelOpened()) {
                        ESP_LOGI("[application]", "Timing self-healing: the listening state automatically opens the audio channel.");
                        app->protocol_->OpenAudioChannel();
                    }
                    if (app->device_state_ == kDeviceStateSpeaking && app->audio_decode_queue_ && app->audio_decode_queue_->empty()) {
                        ESP_LOGI("[application]", "Timing self-healing: speaking but the audio queue is empty, and it will automatically switch back to listening.");
                        app->SetDeviceState(kDeviceStateListening);
                        app->voice_detected_ = true;
                        app->silence_count_ = 10;
                    }
                });
            }
        }
    },
    "self_heal_task", 1024, this, 1, nullptr);

#else
xTaskCreate(
    [](void* arg) {
        Application* app = (Application*)arg;
        int8_t retry_count = 0;
        while (true) {

            vTaskDelay(pdMS_TO_TICKS(1000));

            wifi_ap_record_t ap_info;
            bool wifi_connected = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);

            if (app->GetDeviceState() == kDeviceStateListening && app->protocol_ && (!app->protocol_->IsMqttConnected()) 
                && wifi_connected) {
                ESP_LOGW("MQTT", "MQTT discon., try to redect... ------>002");
                if (retry_count < 5) { // 最多连续重试5次，防止死循环
                    app->protocol_->StartMqttClient();
                    retry_count++;
                } else {
                    ESP_LOGE("MQTT", "MQTTreconnection failed too many times,waiting for network recovery ");
                    vTaskDelay(pdMS_TO_TICKS(6000)); // 失败后延长等待
                    retry_count = 0;
                }
            } else if (app->protocol_ && app->protocol_->IsMqttConnected()) {
                retry_count = 0; // 成功后重置
            }
        }
    },
    "mqtt_self_heal", 4096, this, 1, nullptr);

#endif



#define up_audio 0
      
#if CONFIG_IDF_TARGET_ESP32S3
  audio_processor_.Initialize(codec->input_channels(),
                              codec->input_reference());
  audio_processor_.OnOutput([this](std::vector<int16_t>&& data) {
    background_task_->Schedule([this, data = std::move(data)]() mutable {
        opus_encoder_->Encode(std::move(data),
                              [this](std::vector<uint8_t>&& opus) {
                                Schedule([this, opus = std::move(opus)]() {
                                  protocol_->SendAudio(opus);
                                });
                              });
    });
  });

  wake_word_detect_.Initialize(codec->input_channels(),
                               codec->input_reference());

  wake_word_detect_.OnVadStateChange([this](bool speaking) {
    Schedule([this, speaking]() {

      auto now = std::chrono::steady_clock::now();
      int duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                         now - last_voice_time_)
                         .count();

      if (device_state_ == kDeviceStateSpeaking) {
        voice_detected_ = true;
      }
      if (device_state_ == kDeviceStateListening) {
        // 检测说话状态的变化
        // if (speaking && !state_sepeaking_ && !cancel_tts_sent_) {
        //   // 从非说话状态变为说话状态时发送cancelTTS，并且当前listening周期内未发送过
        //   protocol_->SendCancelTTS();
        //   ESP_LOGI(TAG, "Voice detected, sending CancelTTS");
        //   cancel_tts_sent_ = true; // 标记已在本次listening周期内发送过CancelTTS
        // }

        // voice_detected_ = true;
        if (speaking ) {   
          if (wake_stage_end_) {
            if (++wake_stage_end_cnt_ < 6) {
              return;//<6(100-200ms)帧时，直接返回
            }
            wake_stage_end_cnt_ =0;
            wake_stage_end_ = 0;
          }
          voice_detected_ = true;
          silence_count_ = 0;
          audio_channel_close_ = false;

        } else if (voice_detected_) {  
          wake_stage_end_cnt_ = 0;
          if (duration >= 200) {

            silence_count_++;
            last_voice_time_ = std::chrono::steady_clock::now();

            #if SILENCE_COUNT_DEBUG == 1
            ESP_LOGW("SilenceVAD", "...............Silence count: %d, duration: %dms..................", silence_count_, duration);
            #endif

            if (silence_count_ == 4) {
             
              ESP_LOGI(TAG,          
                       "Silence exceeds 800ms after voice detected, closing "
                       "audio channel");
              protocol_->CloseAudioChannel();

              audio_channel_close_ = true;
              wake_stage_end_  =false;                                
            } else if (silence_count_ == idle_timeout_duration_ * 60) {
              ESP_LOGI(TAG,
                       "Silence exceeds %dms after voice detected, setting "
                       "to idle",
                       idle_timeout_duration_ * 60);
              SetDeviceState(kDeviceStateIdle);
              PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);
              voice_detected_ = false;
              wake_stage_end_  =true;
              // protocol_->SendCancelTTS();
              // protocol_->CloseAudioChannel();
              silence_count_ = 0;
              
            }
          }
        }
      } else if (device_state_ == kDeviceStateSpeaking) {
        voice_detected_ =true;
        // silence_count_ = 30;
        // 可以在这里添加类似的检测，如果需要在Speaking状态下也处理说话变化
        if (state_sepeaking_ == false && speaking) {
          // AbortSpeaking(kAbortReasonWakeWordDetected);
          // protocol_->SendCancelTTS();
          // SetDeviceState(kDeviceStateListening);
        }
      }
      // wake_stage_end_ =true;
      state_sepeaking_ = speaking;  // 更新状态
    });
  });



      wake_word_detect_.OnWakeWordDetected([this](const std::string& wake_word) {
          Schedule([this, wake_word]() { 
            ESP_LOGW(TAG, " >>>> Wake word detected: %s <<<<", wake_word.c_str());

            audio_decode_queue_->clear();
            PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);

            vTaskDelay(pdMS_TO_TICKS(650));
            
            // 无论当前状态如何，先确保音频处理器开启
            audio_processor_.Start();
          
            // 根据当前设备状态进行处理
            if (device_state_ == kDeviceStateIdle) {
              // 重置各种状态
              ResetDecoder();
              opus_encoder_->ResetState();

              // 尝试打开音频通道
              if (!protocol_->OpenAudioChannel()) {
                ESP_LOGE(TAG, "Failed to open audio channel");
                SetDeviceState(kDeviceStateIdle);
                //PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);
                wake_word_detect_.StartDetection();
                return;
              }
              keep_listening_ = true;
              protocol_->SendCancelTTS();
              SetDeviceState(kDeviceStateListening);
              //PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);
              
            } else if (device_state_ == kDeviceStateSpeaking) {

                AbortSpeaking(kAbortReasonWakeWordDetected);
                protocol_->SendCancelTTS();
                mqtt_stop_flag_ = false; 

                SetDeviceState(kDeviceStateListening);
                
                //PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);

            } else if (device_state_ == kDeviceStateListening) {
              // 已经在听了，但还是发送取消TTS并重新开始
              //PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);
              protocol_->SendCancelTTS();
            }

            // 确保唤醒词检测重新开始
            // if (!wake_word_detect_.IsDetectionRunning()) {
            wake_word_detect_.StartDetection();
            // }

            // 确保更新最新的语音时间戳
            last_voice_time_ = std::chrono::steady_clock::now();
            silence_count_ = 11;   
            wake_stage_end_ =true;
            voice_detected_ = true;
          });
        });

  wake_word_detect_.StartDetection();
#endif

  // Initialize the protocol
  // display->SetStatus("初始化协议");

  protocol_ = std::make_unique<MqttProtocol>();
  //
  protocol_->StartMqttClient();
  // while (!protocol_->StartMqttClient()) {
  //   SetDeviceState(kDeviceStateConnecting);
  //   vTaskDelay(pdMS_TO_TICKS(1000));
  //   ESP_LOGW(TAG, "wifi maby not connect....");
  // }
  //
#if WIFI_SIGNAL_CHECK_TONE == 1
auto mqtt = protocol_.get();
if (mqtt) {
    mqtt->OnConnected([this]() {
       
        if (!mqtt_first_connect_flag){
          mqtt_first_connect_flag = true; 
        }else{
        
              Schedule([this]() {
                  // 音频通道未打开，则自动打开
                  if (device_state_ == kDeviceStateListening && !protocol_->IsAudioChannelOpened()) {
                      ESP_LOGI("[application780]", "listen state and audio channel not opened, auto open audio channel");
                      protocol_->OpenAudioChannel();
                  }
                  if (device_state_ == kDeviceStateSpeaking && audio_decode_queue_ && audio_decode_queue_->empty()) {
                      ESP_LOGI("[application785]", "speaking state but audio queue is empty, auto switch to listening");
                      SetDeviceState(kDeviceStateListening);
                      voice_detected_ = true;
                      silence_count_ = 10;
                  }
              });
        }
        

    });
}
#endif




  protocol_->OnNetworkError([this](const std::string& message) {
    Alert("Error", std::move(message));

    #if LOCAL_MOAN_PLAYING == 1
    ESP_LOGW(TAG, "The network is disconnected and enters offline mode: %s", message.c_str());
    Schedule([this]() { SetDeviceState(kDeviceStateOffline); });
    #endif

  });
  //

  protocol_->OnIncomingAudio([this](std::vector<uint8_t>&& data) {

    last_audio_received_time_ = std::chrono::steady_clock::now(); 

    if (device_state_ == kDeviceStateSpeaking) {
      start_play_voice_num_ =0;
      audio_decode_queue_->push_back(std::move(data));
    }
  });
  protocol_->OnAudioChannelOpened([this, codec, &board]() {
    board.SetPowerSaveMode(false);
    if (protocol_->server_sample_rate() != codec->output_sample_rate()) {
      ESP_LOGW(TAG,
               "服务器的音频采样率 %d 与设备输出的采样率 %d "
               "不一致，重采样后可能会失真",
               protocol_->server_sample_rate(), codec->output_sample_rate());
    }
    SetDecodeSampleRate(protocol_->server_sample_rate());
    // 物联网设备描述符
    last_iot_states_.clear();
    auto& thing_manager = iot::ThingManager::GetInstance();
    protocol_->SendIotDescriptors(thing_manager.GetDescriptorsJson());

    #if LOCAL_MOAN_PLAYING == 1
  
    if (device_state_ == kDeviceStateOffline) {
        ESP_LOGI(TAG, "网络恢复，退出offline模式");
        SetDeviceState(kDeviceStateIdle); // 或其它合适的状态
    }
    #endif


    Schedule([this]() {
        if (device_state_ == kDeviceStateListening) {
        //不做任何操作
        } else if (device_state_ == kDeviceStateSpeaking) {

            if (audio_decode_queue_ && audio_decode_queue_->empty()) {
                SetDeviceState(kDeviceStateListening);
                voice_detected_ = true;
                silence_count_ = 10;
            }
        }
        
    });



  });
  protocol_->OnAudioChannelClosed([this, &board]() {
    board.SetPowerSaveMode(true);
    Schedule([this]() {
      // auto display = Board::GetInstance().GetDisplay();
      // display->SetChatMessage("", "");
      ESP_LOGW(TAG, "OnAudioChannelClosed");
      // SetDeviceState(kDeviceStateIdle);
      // SetDeviceState(kDeviceStateListening); // 改为转到 Listening 状态
    });
  });
  protocol_->OnIncomingJson([this](const cJSON* root) {
    if (!root) {
      ESP_LOGW(TAG, "Received null JSON root");
      return;
    }

    // 首先，检查是否是语言设置消息
    cJSON* languagesType = cJSON_GetObjectItem(root, "languagesType");
    if (languagesType && cJSON_IsString(languagesType)) {
      // 这是包含语言设置的消息
      std::string language = languagesType->valuestring;
      ESP_LOGI(TAG, "Received language setting: %s", language.c_str());

      SaveLanguageTypeToNVS(language);
      protocol_->UpdateLanguage(language);
      return;  // 已处理完此消息，直接返回
    }
    //
    cJSON* control_moan_type = cJSON_GetObjectItem(root, "control_moan");
    if (control_moan_type) {

      ESP_LOGI(TAG, "Received control_moan message: %s", control_moan_type->valuestring);
      const char* type_str = control_moan_type->valuestring;
      if (strcmp(type_str, "0")==0) {

        moaning_flag_ = false; 

        ESP_LOGI(TAG, "Setting moaning_state to OFF");
        moaning_state_.first = false;
        moaning_state_.second.clear();
      } else if (strcmp(type_str, "1")==0) {

        moaning_flag_ = true; 

        ESP_LOGI(TAG, "Setting moaning_state to ON with empty string");
        moaning_state_.first = true;
        moaning_state_.second = "";
      } else {
        moaning_flag_ = true;

        ESP_LOGI(TAG, "Setting moaning_state to ON with custom value: %s", type_str);
        moaning_state_.first = true;
        moaning_state_.second = std::string(control_moan_type->valuestring);
      }
      return;
    }

#if LOCAL_MOAN_PLAYING == 1
    //json “arrears”
    cJSON* arrears = cJSON_GetObjectItem(root, "arrears");
    if (arrears && cJSON_IsTrue(arrears)) {
        ESP_LOGW(TAG, "收到欠费/单机命令，进入offline模式");
        Schedule([this]() { SetDeviceState(kDeviceStateOffline); });
        return;
    }
    //json“offline”
    cJSON* offline = cJSON_GetObjectItem(root, "offline");
    if (offline && cJSON_IsTrue(offline)) {
        ESP_LOGW(TAG, "收到offline命令，进入offline模式");
        Schedule([this]() { SetDeviceState(kDeviceStateOffline); });
        return;
    }
#endif



    // 如果不是语言设置消息，再检查 type 字段
    cJSON* type = cJSON_GetObjectItem(root, "type");
    if (!type || !cJSON_IsString(type)) {
      ESP_LOGW(TAG, "Missing or invalid type field");
      return;
    }

    const char* type_str = type->valuestring;
    ESP_LOGI(TAG, "Processing JSON message with type: %s", type_str);

    // 处理不同类型的消息
    if (strcmp(type_str, "tts") == 0) {
      // 处理TTS消息
      auto state = cJSON_GetObjectItem(root, "state");
      if (!state || !cJSON_IsString(state)) {
        ESP_LOGW(TAG, "Invalid TTS state");
        return;
      }

      if (strcmp(state->valuestring, "start") == 0) {
        SetDeviceState(kDeviceStateSpeaking);
        start_play_voice_  = true; 
        start_play_voice_num_ = 0;
        // std::lock_guard<std::mutex> lock(mutex_);
        audio_decode_queue_->clear();

        playback_start_time_ = std::chrono::steady_clock::now();

        ESP_LOGI(TAG, "Starting playback with sample rate: %d Hz",
                 opus_decode_sample_rate_);
        auto codec = Board::GetInstance().GetAudioCodec();
        if (!codec) {
          ESP_LOGE(TAG, "Codec is null");
          return;
        }
        ESP_LOGI(TAG, "Codec output sample rate: %d Hz",
                 codec->output_sample_rate());

        codec->EnableOutput(true);
      } else if (strcmp(state->valuestring, "stop") == 0) {
        audio_decode_queue_->FlushSegmentBuffer();

        #if 1
         Schedule([this]() {
           start_play_voice_ = false;
           start_play_voice_num_ = 0;

            if (device_state_ == kDeviceStateSpeaking) {
             mqtt_stop_flag_ = true; 
            }
             //ESP_LOGI("[MQTT_JSON]", "__stop flag_set_mqtt_stop_flag_ = %d", mqtt_stop_flag_.load());
        });
        #else
        start_play_voice_num_ = 0;
        start_play_voice_ = false; 
        //ESP_LOGI("[MQTT_JSON]", "start_play_voice_num_ = %d, start_play_voice_ = %d",start_play_voice_num_, start_play_voice_);
        #endif

      } else if (strcmp(state->valuestring, "sentence_start") == 0) {
        // auto text = cJSON_GetObjectItem(root, "text");
        // if (text && cJSON_IsString(text) && display) {
        //     ESP_LOGI(TAG, "<< %s", text->valuestring);
        //     display->SetChatMessage("assistant", text->valuestring);
        // }
      }
    } else if (strcmp(type_str, "0") == 0 || strcmp(type_str, "1") == 0 ||
               strcmp(type_str, "2") == 0 || strcmp(type_str, "3") == 0) {
      // 这是控制消息，直接处理
      cJSON* vlue = cJSON_GetObjectItem(root, "vlue");
      if (!vlue || !cJSON_IsString(vlue)) {
        ESP_LOGW(TAG, "Missing or invalid vlue field in control message");
        return;
      }

      int type_val = atoi(type_str);  // 将类型字符串转换为整数
      std::string control_value = vlue->valuestring;

      ESP_LOGI(TAG, "Processing control message: type=%d, value=%s", type_val,
               control_value.c_str());

      if (type_val == 0) {  // 音量控制
        auto codec = Board::GetInstance().GetAudioCodec();
        if (!codec) {
          ESP_LOGE(TAG, "Codec is null");
          return;
        }

        int new_volume = current_volume_;
        if (control_value == "+") {
          new_volume = current_volume_ + kVolumeStep;
        } else if (control_value == "-") {
          new_volume = current_volume_ - kVolumeStep;
        } else if (control_value == "++") {
          new_volume = kVolumeMax;
        } else if (control_value == "--") {
          new_volume = kVolumeMin;
        } else {
          try {
            new_volume = std::stoi(control_value);
          } catch (const std::exception& e) {
            ESP_LOGW(TAG, "Invalid volume value: %s", control_value.c_str());
            return;
          }
        }
        new_volume = std::max(kVolumeMin, std::min(100, new_volume));

        ESP_LOGW(TAG, "Setting volume: %d -> %d", current_volume_, new_volume);
        codec->SetOutputVolume(new_volume);
        current_volume_ = new_volume;
        SaveVolumeToNVS(new_volume);
      } else if (type_val == 1) {  // 关机控制
        ESP_LOGW(TAG, "Shutdown requested");
        auto& board = Board::GetInstance();
        SetPowerOffset(false);
        vTaskDelay(pdMS_TO_TICKS(1000));
        esp_deep_sleep_start();
      } else if (type_val == 2) {  // 关机控制
        ESP_LOGW(TAG, "Setting idle timeout duration: %s",
                 control_value.c_str());
        if (control_value == "10") {
          idle_timeout_duration_ = 10;
        } else if (control_value == "20") {
          idle_timeout_duration_ = 20;
        } else if (control_value == "30") {
          idle_timeout_duration_ = 30;
        } else if (control_value == "40") {
          idle_timeout_duration_ = 40;
        } else if (control_value == "50") {
          idle_timeout_duration_ = 50;
        } else if (control_value == "60") {
          idle_timeout_duration_ = 60;
        }
      } else if (type_val == 3) {  // 等待唤醒状态
        ESP_LOGW(TAG, "Entering idle mode, waiting for wake word");

        if (device_state_ == kDeviceStateSpeaking) {
          AbortSpeaking(kAbortReasonNone);
        }

        // CloseAudioChannel();
        SetDeviceState(kDeviceStateIdle);
      }
    } else if (strcmp(type_str, "iot") == 0) {
      auto commands = cJSON_GetObjectItem(root, "commands");
      if (commands && cJSON_IsArray(commands)) {
        auto& thing_manager = iot::ThingManager::GetInstance();
        for (int i = 0; i < cJSON_GetArraySize(commands); ++i) {
          auto command = cJSON_GetArrayItem(commands, i);
          if (command) {
            thing_manager.Invoke(command);
          }
        }
      }
    }
  });

  // PlayOpusFile(output_link_zh_start, output_link_zh_end -
  // output_link_zh_start);
  protocol_->WakeupCall();
  SetDeviceState(kDeviceStateIdle);


  #if Use_433_UART == 1
    UART_433_Init();

    BaseType_t result =  xTaskCreate([](void *){
      //ESP_LOGI("Thread=UART_433_RX_Task", "--------------UART_433_RX_Task_Thread_Core %d---------------", xPortGetCoreID());

      #if SEND_MAC_ADDRESS_433 == 1
       static uint16_t uart_433_tx_cnt = 600;
      #endif

        while (true) {
            UART_433_RX_DATA();  

            #if SEND_MAC_ADDRESS_433 == 1
            if(!OTA_UPGRADE_FLAG){
              if( (uart_433_tx_cnt > 0) && (uart_433_tx_cnt <= 600)) {
                UART_433_TX_DATA(SystemInfo::GetMacAddress().c_str());
                uart_433_tx_cnt--;
              }
            }
            #endif

            vTaskDelay(100 / portTICK_PERIOD_MS);  
        }
    }, "UART_433_RX_Task", 4096, NULL, 1, NULL); 

    if(result != pdPASS){
        ESP_LOGE("UART", "======Failed to create UART 433 RX task!======");
    }else{
        ESP_LOGI("UART", "=======UART RX 433 task created successfully.======");
    }
    #endif


    #if SEND_MAC_ADDRESS_TASK == 1
    xTaskCreate([](void* arg) {
        // 只在不是升级状态时发送
        auto& app = Application::GetInstance();

        if (app.GetDeviceState() != kDeviceStateUpgrading) {
            for (uint16_t i = 0; i < 600; ++i) { // 600次，每次100ms，共60秒
                UART_433_TX_DATA(SystemInfo::GetMacAddress().c_str());
                vTaskDelay(pdMS_TO_TICKS(100));
            }
            ESP_LOGI("MAC_TX", "MAC TX finished");
        } 
           
        vTaskDelete(NULL);
        },"mac_tx_task",1024,nullptr,2,nullptr
    );
   #endif



   #if SEND_MAC_ADDRESS_TIME == 1

    static esp_timer_handle_t mac_tx_timer = nullptr;
    static uint16_t mac_tx_count = 0;

    auto mac_tx_timer_callback = [](void* arg) {
        auto& app = Application::GetInstance();
        if (app.GetDeviceState() != kDeviceStateUpgrading) {
            UART_433_TX_DATA(SystemInfo::GetMacAddress().c_str());
        }
        mac_tx_count++;
        if (mac_tx_count >= 600) { // 600次，每次100ms，共60秒
            esp_timer_stop(mac_tx_timer);
            esp_timer_delete(mac_tx_timer);
            mac_tx_timer = nullptr;
            ESP_LOGI("MAC_TX", "MAC TX finished (esp_timer)");
        }
    };

    mac_tx_count = 0;
    esp_timer_create_args_t timer_args = {
        .callback = mac_tx_timer_callback,
        .arg = nullptr,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "mac_tx_timer"
    };
    esp_timer_create(&timer_args, &mac_tx_timer);
    esp_timer_start_periodic(mac_tx_timer, 100 * 1000); // 100ms, 单位: 微秒

  #endif

   

}



void Application::Schedule(std::function<void()> callback) {
  std::lock_guard<std::mutex> lock(mutex_);
  main_tasks_.push_back(std::move(callback));
  xEventGroupSetBits(event_group_, SCHEDULE_EVENT);
}

// 新增方法: 音频输入处理循环
void Application::InputAudioLoop() {
  while (true) {
    auto bits = xEventGroupWaitBits(event_group_, AUDIO_INPUT_READY_EVENT,
                                    pdTRUE, pdFALSE, portMAX_DELAY);

    if (bits & AUDIO_INPUT_READY_EVENT) {
      InputAudio();
    }
  }
}

// The Main Loop controls the chat state and websocket connection
// If other tasks need to access the websocket or chat state,
// they should use Schedule to call this function
void Application::MainLoop() {

  while (true) {
    auto bits = xEventGroupWaitBits(event_group_,
                                    SCHEDULE_EVENT ,
                                    pdTRUE, pdFALSE, portMAX_DELAY);

    // if (bits & AUDIO_OUTPUT_READY_EVENT) {
    //   OutputAudio();
    // }
    if (bits & SCHEDULE_EVENT) {
      mutex_.lock();
      std::list<std::function<void()>> tasks = std::move(main_tasks_);
      mutex_.unlock();
      for (auto& task : tasks) {
        task();
      }
    }

    
    #if LOCAL_MOAN_PLAYING == 1
    //断网进offline
    wifi_ap_record_t ap_info;
    bool wifi_connected = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);
    if (wifi_connected){
        NetOff_OFFLINE_FLAG = false;
    }else{
        NetOff_OFFLINE_FLAG = true;
    }
    #endif


  }
}


//
double v_test = 4.0;
void Application::ImuRead() {

  // 记录上一个动作状态
  static int last_motion_state = MOTION_LEVEL_IDLE;  // 初始化为-1表示未知状态
  static int64_t last_send_time = 0;  // 上次发送时间
  //
  uint8_t imu_data_motion = MOTION_LEVEL_IDLE;
  //
  std::string last_moan_name;

  //
  std::map<int, std::vector<std::string>> imu_motion_moan_string;
  for (const auto& moan_string : kMoaningStringIndex) {
    imu_motion_moan_string[moan_string.first[1] - '0'].push_back(moan_string.first);
  }
  //
  while (true) {
    //------------------------------------------------------------
        // // 先尝试从队列取433事件（非阻塞或带超时）
        // if (xQueueReceive(uart_433_event_queue, &event, 0)) {
        //     button_value_int_local = event.button_value_int;
        //     key_433_press_local = event.key_press;
        // }
    //------------------------------------------------------------
//ESP_LOGI("IMU", "Received control_moan message: %d", moaning_flag_);


    // vTaskDelay(300);  // 增加到300ms采样间隔，减少CPU占用
    vTaskDelay(500 / portTICK_PERIOD_MS);  //现在是500ms执行一次
    double vo = Board::GetInstance().GetBattary();
    
    // if (v_test > 2.0) {
    //   v_test -= 0.02;
    // }
    // vo = v_test;
    // // batter_volo_-=0.02;
    int low_batter_audio_play_cnt = 0;
    batter_volo_ = batter_volo_ * 0.85 + vo * 0.15;
    // int level = gpio_get_level(GPIO_NUM_48);
    // ESP_LOGI("GPIO", "GPIO48 level: %d", level);
        //ESP_LOGW("BAT", "battery voltage:%f", batter_volo_);
    if ((gpio_get_level(GPIO_NUM_48) == 0)) {
      if (batter_volo_ < 3.6) {
        // ESP_LOGW("BAT", "battery voltage:%f", batter_volo_);
        batter_state_ = true;
        if (device_state_ == kDeviceStateIdle) {
          ESP_LOGW("BAT", "battery voltage:%f", batter_volo_);
          // codec->EnableInput(flse);  // 关闭麦克风输入
          if (++low_batter_audio_play_cnt > 4) {
            low_batter_audio_play_cnt = 0;
            Schedule([this]() {
              if (oudio_output_finish_) {
                oudio_output_finish_ = false;
               
                //
                PlayOpusFile(p3_dong_start, p3_dong_end - p3_dong_start);
                //
              }
            });
          }
        }
      }

      if (batter_volo_ < 3.3) {
        SetPowerOffset(0);
      } else if (batter_volo_ > 3.6) {
        batter_state_ = false;
        low_batter_audio_play_cnt = 0;
      }
    }else {
        batter_state_ = false;
    }


    // 防止开始了start后网络原因没有发stop下来
    //start_play_voice_：标志当前是否处于“正在播放语音”状态。
    //oudio_output_finish_：标志音频输出是否已经完成（即音频播放完毕）。
    // Schedule([this]() {
    //   if (start_play_voice_ && oudio_output_finish_) {
    //     //start_play_voice_num_：计数器，
    //     //每次检测到 start_play_voice_ && oudio_output_finish_ 时自增
    //     //500ms 进入一次， 20 *500ms = 10000ms = 10s
    //     if (start_play_voice_num_++ > 20) {
    //       SetDeviceState(kDeviceStateListening);
    //       ESP_LOGI("[TEST_07]", "**************** TEST_07 *****************");
    //       start_play_voice_ = 0;
    //       start_play_voice_num_ = 0;
    //     }
    //   }
    // });

    if (last_batter_state_ != batter_state_) {
      auto led = Board::GetInstance().GetLed();
      led->OnStateChanged();
    }
    last_batter_state_ = batter_state_;

    auto imu_data = qmi8658_motion_demo();
    int current_motion = imu_data.motion;
    int64_t current_time = esp_timer_get_time() / 1000;  // 当前时间(毫秒)
    //
    if (moaning_state_.first) {
      // if (current_motion != last_motion_state) {
      //   if (current_motion == MOTION_LEVEL_IDLE) {
      //     Schedule([this, current_motion, &last_moan_name]() {
      //       SetDeviceState(kDeviceStateListening);
      //     });
      //     audio_decode_queue_->clear();
      //     last_moan_name.clear();
      //   } else if (moaning_state_.second.empty()) {
      //     int rand_moan_index =
      //         rand() % imu_motion_moan_string[current_motion].size();
      //     const std::string rand_moan_name =
      //         imu_motion_moan_string[current_motion][rand_moan_index];
      //     Schedule([this, rand_moan_name]() {
      //       SetDeviceState(kDeviceStateMoaning);
      //       audio_decode_queue_->LoopPlayLocalFile(
      //           kMoaningStringIndex[rand_moan_name].first,
      //           kMoaningStringIndex[rand_moan_name].second);
      //     });
      //   } else if (last_moan_name != moaning_state_.second) {
      //     Schedule([this, current_motion, &last_moan_name]() {
      //       SetDeviceState(kDeviceStateMoaning);
      //       audio_decode_queue_->LoopPlayLocalFile(
      //           kMoaningStringIndex[moaning_state_.second].first,
      //           kMoaningStringIndex[moaning_state_.second].second);
      //     });
      //     last_moan_name = moaning_state_.second;
      //   }
      // }
    }

    #if FF_IMU_PARAM_IS_Wire == 1
    auto touch_value = Board::GetInstance().GetTouchKey();

    #elif FF_IMU_PARAM_IS_Wire == 0
    //auto touch_value = button_value_int;
    #endif


        // 判断与上次状态是否不同且满足最小发送间隔
      if (current_motion != last_motion_state &&
          (current_time - last_send_time > 600)) {  // 至少2秒发送一次

      //   // 状态变化且满足时间间隔，发送
      //   Schedule([this, current_motion]() {
      //     if (!protocol_) return;

      //     // 记录并发送新状态
      //     if (current_motion == 0) {
      //       ESP_LOGW("IMU", "Motion changed to IDLE (0)");
      //     } else {
      //       ESP_LOGW("IMU", "Motion changed to: %d", current_motion);
      //     }

      //     protocol_->SendImuStatesAndValue(current_motion);
      //   });

      //   // 更新状态和发送时间
      //   last_send_time = current_time;
      // } else if (current_motion != last_motion_state) {
      //   // 只更新状态，不发送
      // }
    }
    

    #if FF_IMU_PARAM_IS_Wire == 1

    int touch_value_temp = 0;
    for (int i = 0; i < touch_value.size(); i++) {
      // ESP_LOGW(TAG, "touch_value %d",int( touch_value[i]));
      touch_value_temp <<= 1;
      if (touch_value[i]) {
        touch_value_temp |= 0x01;
      }
    }

    #elif FF_IMU_PARAM_IS_Wire == 0
    auto touch_value_temp = (button_value_int/2); 
    //if(touch_value_temp == button_value_int) first_key = true;

    #endif
    

    #if FF_IMU_PARAM_IS_Wire == 1

    // ESP_LOGW(TAG, "touch_value %d",int( touch_value_temp));
    // ESP_LOGW(TAG, "touch_value %d", touch_value_temp);
    if (current_motion != last_motion_state ||
        touch_value_temp != touch_value_) {
      Schedule([this, imu_data, touch_value_temp]() {
        if (!protocol_) return;

        // // 记录并发送新状态
        // if (current_motion == 0) {
        //   ESP_LOGW("IMU", "Motion changed to IDLE (0)");
        // } else {
        //   ESP_LOGW("IMU", "Motion changed to: %d", current_motion);
        // }
        protocol_->SendImuStatesAndValue(imu_data, touch_value_temp);
      });
    }


    #elif FF_IMU_PARAM_IS_Wire == 0  //无线模式下

#if LOCAL_MOAN_PLAYING == 1

    #if 0
      if ((current_motion != last_motion_state)) {
          moan_count += 1;
          if(moan_count >= 254) moan_count = 1;
      }
    #else
    ///使用随机数来选择音频
      if ((current_motion != last_motion_state)) {

        IMU_CHANGE_FLAG = true;
        moan_count = (uint8_t)(esp_random() % 61) + 10; 

      }else if(key_433_press == true){
        
        IMU_CHANGE_FLAG = true;
        moan_count = (uint8_t)(esp_random() % 61) + 10; 
        button_value_int = 0; 
        key_433_press = false;
          
      }
      

    #endif
#endif
    //ESP_LOGW("IMU_DATA", "--=====ooooo=====-- IMU Data=  %s --======ooooo=====-- ", imu_data.ToString().c_str());
      if (OFFLINE_FLAG && NetOff_OFFLINE_FLAG)
      {

#if LOCAL_MOAN_PLAYING == 1
        #if 1
       
        ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--moan_count : %d--======ooooo=====-- ",moan_count);
            if((moan_count >= 10 && moan_count < 15)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
              ESP_LOGW("Local_moan", "--=====ooooo=====--moan 01 --======ooooo=====-- ");
              oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000020_start, p3_moan_1000020_end - p3_moan_1000020_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true;
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false;
                  
              } else if((moan_count >= 15 && moan_count < 20)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 02 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000021_start, p3_moan_1000021_end - p3_moan_1000021_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              } else if((moan_count >= 20 && moan_count < 25)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 03 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000022_start, p3_moan_1000022_end - p3_moan_1000022_start); 
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              } else if((moan_count >= 25 && moan_count < 30)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 04 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000023_start, p3_moan_1000023_end - p3_moan_1000023_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              }else if((moan_count >= 30 && moan_count < 35)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 05 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000024_start, p3_moan_1000024_end - p3_moan_1000024_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              }else if((moan_count >= 35 && moan_count < 40)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 06 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000025_start, p3_moan_1000025_end - p3_moan_1000025_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 
              }else if((moan_count >= 40 && moan_count < 45)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 07 --======ooooo=====-- ");
                oudio_output_finish_ = false; // 1. 清零
                PlayOpusFile(p3_moan_1000026_start, p3_moan_1000026_end - p3_moan_1000026_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { // 3. 等待最多60秒
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              }else if((moan_count >= 45 && moan_count < 50)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 08 --======ooooo=====-- ");
                oudio_output_finish_ = false; // 1. 清零
                PlayOpusFile(p3_moan_1000027_start, p3_moan_1000027_end - p3_moan_1000027_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              }else if((moan_count >= 50 && moan_count < 55)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 09 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000028_start, p3_moan_1000028_end - p3_moan_1000028_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false;

              }else if((moan_count >= 55 && moan_count < 60)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 10 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000029_start, p3_moan_1000029_end - p3_moan_1000029_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) {
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              }else if((moan_count >= 60 && moan_count < 65)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 11 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000030_start, p3_moan_1000030_end - p3_moan_1000030_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              }else if((moan_count >= 65 && moan_count <= 70)&&(local_moan_is_playing == false)&&(IMU_CHANGE_FLAG)) {
                ESP_LOGW("Local_moan", "--=====ooooo=====--moan 012 --======ooooo=====-- ");
                oudio_output_finish_ = false; 
                PlayOpusFile(p3_moan_1000031_start, p3_moan_1000031_end - p3_moan_1000031_start);
                uint16_t wait_ms = 0;
                while (!oudio_output_finish_ && wait_ms < 60000) { 
                    vTaskDelay(pdMS_TO_TICKS(100));
                    wait_ms += 100;
                }
                local_moan_is_playing = true; 
                audio_decode_queue_->clear(); 
                IMU_CHANGE_FLAG = false; 

              }else if (local_moan_is_playing && oudio_output_finish_) {
                  local_moan_is_playing = false; 
              }


        ESP_LOGW("END_LOCAL_MOAN", "--=====ooooo=====--END_LOCAL_MOAN--======ooooo=====-- ");

        #else

        if ((current_motion != last_motion_state)) {
          ESP_LOGW("IMU_DATA", "--=====ooooo=====-- IMU Data=  %s --======ooooo=====-- ", imu_data.ToString().c_str());
            moan_count += 1;
            if(moan_count >= 254) moan_count = 1;

            ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--moan_count : %d--======ooooo=====-- ",moan_count);


            //// -------------------------音频1
            if ((moan_count == 3) && (local_moan_is_playing == false)) {
            oudio_output_finish_ = false; // 1. 清零
            PlayOpusFile(p3_moan_1000022_start, p3_moan_1000022_end - p3_moan_1000022_start); // 2. 播放
            uint16_t wait_ms = 0;
            while (!oudio_output_finish_ && wait_ms < 60000) { // 3. 等待最多60秒
                vTaskDelay(pdMS_TO_TICKS(100));
                wait_ms += 100;
            }
            local_moan_is_playing = true; // 4. 播放完再置位
            audio_decode_queue_->clear(); //清空音频队列
         }

            // if ((moan_count == 3) && (local_moan_is_playing == false)) {
            //   PlayOpusFile(p3_moan_1000022_start, p3_moan_1000022_end - p3_moan_1000022_start);
            //   vTaskDelay(pdMS_TO_TICKS(7200));
            //   local_moan_is_playing = true;
            // }


            // 通过位操作直接调用对应音频
            // 每个位对应一个音频资源（仅12个）
            // if (moan_count == 3){ 

            // auto codec = Board::GetInstance().GetAudioCodec();
            // codec->EnableOutput(true); // 确保输出开启
            // oudio_output_finish_ = false;
            // PlayOpusFile(p3_moan_1000021_start, p3_moan_1000021_end - p3_moan_1000021_start);
            // int wait_ms = 0;
            // while (!oudio_output_finish_ && wait_ms < 60000) {
            //     vTaskDelay(pdMS_TO_TICKS(100));
            //     wait_ms += 100;
            // }
            // codec->EnableOutput(false); // 播放完再关闭

            //   }
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--21--======ooooo=====-- ");}
          //  // vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 23) PlayOpusFile(p3_moan_1000022_start, p3_moan_1000022_end - p3_moan_1000022_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--22--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 43) PlayOpusFile(p3_moan_1000023_start, p3_moan_1000023_end - p3_moan_1000023_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--23--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 63) PlayOpusFile(p3_moan_1000024_start, p3_moan_1000024_end - p3_moan_1000024_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--24--======ooooo=====-- ");
          //  // vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 83) PlayOpusFile(p3_moan_1000025_start, p3_moan_1000025_end - p3_moan_1000025_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--25--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 103) PlayOpusFile(p3_moan_1000026_start, p3_moan_1000026_end - p3_moan_1000026_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--26--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 123) PlayOpusFile(p3_moan_1000027_start, p3_moan_1000027_end - p3_moan_1000027_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--27--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 143) PlayOpusFile(p3_moan_1000028_start, p3_moan_1000028_end - p3_moan_1000028_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--28--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 163) PlayOpusFile(p3_moan_1000029_start, p3_moan_1000029_end - p3_moan_1000029_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--29--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 183) PlayOpusFile(p3_moan_1000030_start, p3_moan_1000030_end - p3_moan_1000030_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--30--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
          //   if (moan_count == 203) PlayOpusFile(p3_moan_1000031_start, p3_moan_1000031_end - p3_moan_1000031_start);
          //   ESP_LOGW("IMU_Local_moan", "--=====ooooo=====--31--======ooooo=====-- ");
          //   //vTaskDelay(pdMS_TO_TICKS(10000));
         }
         else if (key_433_press == true)
         {
          
            button_value_int = 0; 
            key_433_press = false;
         }
        #endif
#endif
      }
      else
      {//非offlien

          if ((current_motion != last_motion_state) || (key_433_press == true)) {
            ESP_LOGW("application", "----------** Key_433_Press=  %d **------- ", key_433_press);
            Schedule([this, imu_data, touch_value_temp]() {
              if (!protocol_) return;

              #if 1 
                  ESP_LOGW("IMU", "===============moaning_flag_ : %d=================",moaning_flag_);
                  if (moaning_flag_ == true)
                  {
                    protocol_->SendImuStatesAndValue(imu_data, touch_value_temp);
                  }
              #else 
                  protocol_->SendImuStatesAndValue(imu_data, touch_value_temp);
              #endif

              
            });

            button_value_int = 0; 
            key_433_press = false;
          }

        }
      #endif  

    touch_value_ = touch_value_temp;
    last_motion_state = current_motion;
    // protocol_->SendImuStatesAndValue(current_motion);
  }
}



void Application::OutputAudio() {
  auto codec = Board::GetInstance().GetAudioCodec();
  auto now = std::chrono::steady_clock::now();

}

void Application::ResetDecoder() {
  auto codec = Board::GetInstance().GetAudioCodec();

//   // 设置解码器采样率为16kHz（与编码时相同）
//   opus_decode_sample_rate_ = 16000;
//   opus_decoder_ =
//       std::make_unique<OpusDecoderWrapper>(opus_decode_sample_rate_, 1);

//   // 确保有锁保护清空队列操作
//   {
//     std::lock_guard<std::mutex> lock(mutex_);
    audio_decode_queue_->clear();

//   }

//   last_output_time_ = std::chrono::steady_clock::now();
//   aborted_ = false;
//   stop_after_playback_ = false;

//   // 确保音频输出已启用并设置正确的音量
//   codec->EnableOutput(true);
  // codec->SetOutputVolume(80);
}

void Application::InputAudio() {
  auto codec = Board::GetInstance().GetAudioCodec();
  std::vector<int16_t> data;
  if (!codec->InputData(data)) {
    return;
  }

  // 优化重采样逻辑，避免过多的内存分配
  if (codec->input_sample_rate() != 16000) {
    static std::vector<int16_t> resampled_buffer;  // 重用buffer
    if (codec->input_channels() == 2) {
      static std::vector<int16_t> mic_channel;
      static std::vector<int16_t> reference_channel;

      mic_channel.resize(data.size() / 2);
      reference_channel.resize(data.size() / 2);

      for (size_t i = 0, j = 0; i < mic_channel.size(); ++i, j += 2) {
        mic_channel[i] = data[j];
        reference_channel[i] = data[j + 1];
      }

      resampled_buffer.resize(
          input_resampler_.GetOutputSamples(mic_channel.size()) * 2);
      auto resampled_mic = std::vector<int16_t>(
          resampled_buffer.begin(),
          resampled_buffer.begin() + resampled_buffer.size() / 2);
      auto resampled_reference = std::vector<int16_t>(
          resampled_buffer.begin() + resampled_buffer.size() / 2,
          resampled_buffer.end());

      input_resampler_.Process(mic_channel.data(), mic_channel.size(),
                               resampled_mic.data());
      reference_resampler_.Process(reference_channel.data(),
                                   reference_channel.size(),
                                   resampled_reference.data());

      data = std::move(resampled_buffer);
    } else {
      resampled_buffer.resize(input_resampler_.GetOutputSamples(data.size()));
      input_resampler_.Process(data.data(), data.size(),
                               resampled_buffer.data());
      data = std::move(resampled_buffer);
    }
  }

#if CONFIG_IDF_TARGET_ESP32S3
  // 分离麦克风和参考信号（如果是双声道）
  std::vector<int16_t> mic_data, ref_data;
  if (codec->input_channels() == 2 && codec->input_reference()) {
    // 双声道模式：分离麦克风和参考信号
    mic_data.reserve(data.size() / 2);
    ref_data.reserve(data.size() / 2);

    for (size_t i = 0; i < data.size(); i += 2) {
      mic_data.push_back(data[i]);     // 通道0：麦克风
      ref_data.push_back(data[i + 1]); // 通道1：参考信号
    }
  } else {
    // 单声道模式：只有麦克风数据
    mic_data = data;
    ref_data.clear(); // 空的参考信号
  }

  // 在Speaking状态下，只进行唤醒词检测，不处理其他音频输入
  if (device_state_ == kDeviceStateSpeaking /*||
      device_state_ == kDeviceStateMoaning*/) {
    // 只有在唤醒词检测需要运行时才处理音频输入
    if (wake_word_detectinput_reference_.IsDetectionRunning()) {
      if (codec->() && !ref_data.empty()) {
        wake_word_detect_.Feed(mic_data, ref_data);  // 使用分离的信号
      } else {
        wake_word_detect_.Feed(data);  // 使用原始数据
      }
    }
    // 在Speaking状态下不处理其他音频输入，避免自己的声音被录入
    return;
  }

  // 正常情况下的处理
  if (wake_word_detect_.IsDetectionRunning()) {
    if (codec->input_reference() && !ref_data.empty()) {
      wake_word_detect_.Feed(mic_data, ref_data);  // 使用分离的信号进行AEC
    } else {
      wake_word_detect_.Feed(data);  // 使用原始数据
    }
  }

  if (audio_processor_.IsRunning() &&
      device_state_ == kDeviceStateListening) {  // && !audio_channel_close_) {
    audio_processor_.Input(data);  // AudioProcessor仍使用原始交织数据
  }
#else
  // 非ESP32S3处理器的情况
  if (device_state_ == kDeviceStateListening) {
    background_task_->Schedule([this, data = std::move(data)]() mutable {
      opus_encoder_->Encode(std::move(data),
                            [this](std::vector<uint8_t>&& opus) {
                              Schedule([this, opus = std::move(opus)]() {
                                protocol_->SendAudio(opus);
                              });
                            });
    });
  }
#endif
}

void Application::AbortSpeaking(AbortReason reason) {
  ESP_LOGI(TAG, "Abort speaking");
  aborted_ = true;

  // 清空音频队列等其他操作..m.
//   std::lock_guard<std::mutex> lock(mutex_);
  audio_decode_queue_->clear();

}

void Application::SetDeviceState(DeviceState state) {
  // 如果当前处于升级状态，不允许切换到其他状态
  
  if (device_state_ == kDeviceStateUpgrading &&
      state != kDeviceStateUpgrading) {
    ESP_LOGW(TAG, "Device is upgrading, ignoring state change request to %s",
             STATE_STRINGS[state]);
    return;
  }

  if (device_state_ == state) {
    return;
  }

  ESP_LOGW(TAG, "STATE: %s -> %s", STATE_STRINGS[device_state_],
           STATE_STRINGS[state]);

  if (device_state_ == kDeviceStateSpeaking && state == kDeviceStateIdle) {
    // 等待所有音频数据播放完成
    // std::unique_lock<std::mutex> lock(mutex_);
    if (!audio_decode_queue_->empty()) {
      return;  // 还有数据未播放完，暂不切换状态
    }
  }

  device_state_ = state;

  // The state is changed, wait for all background tasks to finish
  background_task_->WaitForCompletion();

  // auto display = Board::GetInstance().GetDisplay();
  auto led = Board::GetInstance().GetLed();
  led->OnStateChanged();

#if LOCAL_MOAN_PLAYING == 1
    if (state == kDeviceStateOffline) {
        OFFLINE_FLAG = true;
    } else {
        OFFLINE_FLAG = false;
    }
#endif

  switch (state) {
    case kDeviceStateUnknown:
    case kDeviceStateIdle:
      // display->SetStatus("待命");
      // display->SetEmotion("neutral");
      ResetDecoder();
      opus_encoder_->ResetState();
#ifdef CONFIG_IDF_TARGET_ESP32S3
      audio_processor_.Stop();
#endif
      break;
    case kDeviceStateConnecting:
      // display->SetStatus("连接中...");
      break;
    case kDeviceStateListening:{


#if CONFIG_IDF_TARGET_ESP32S3
      audio_processor_.Start();
#endif
      ResetDecoder();
      // cancel_tts_sent_ = false; // 重置标志，允许在新的listening周期发送CancelTTS
      UpdateIotStates();
      break;
    }
    case kDeviceStateSpeaking:
      // display->SetStatus("说话中...");
      ResetDecoder();
      // opus_encoder_->ResetState();
#if CONFIG_IDF_TARGET_ESP32S3
      // 不停止音频处理器
      audio_processor_.Stop();  // 删除或注释掉

      // 确保唤醒词检测也在运行
      if (!wake_word_detect_.IsDetectionRunning()) {
        wake_word_detect_.StartDetection();
      }
#endif
      break;
    case kDeviceStateUpgrading:
      // 处理升级状态 - 确保所有音频处理都被禁用
      {
        auto codec = Board::GetInstance().GetAudioCodec();
        codec->EnableOutput(false);
        codec->EnableInput(false);

        // 清空音频缓冲区
        // std::lock_guard<std::mutex> lock(mutex_);
        audio_decode_queue_->clear();

      }
#if CONFIG_IDF_TARGET_ESP32S3
      
      audio_processor_.Stop();
      wake_word_detect_.StopDetection();
      audio_decode_queue_->Stop();
#endif
      break;

#if LOCAL_MOAN_PLAYING == 1 
    case kDeviceStateOffline: {
    #if 1
        auto codec = Board::GetInstance().GetAudioCodec();
        // 只关闭输入，保留输出
        codec->EnableInput(false);
        if (audio_decode_queue_) {
            audio_decode_queue_->clear();
        }
        #if CONFIG_IDF_TARGET_ESP32S3
        audio_processor_.Stop();
        wake_word_detect_.StopDetection();
        #endif
        
        break;

    #else
        auto codec = Board::GetInstance().GetAudioCodec();
        codec->EnableOutput(false);
        codec->EnableInput(false);

        if (audio_decode_queue_) {
            audio_decode_queue_->clear();
        }

        #if CONFIG_IDF_TARGET_ESP32S3
        audio_processor_.Stop();
        wake_word_detect_.StopDetection();
        #endif
        break;
    #endif
    }
#endif

    default:
      // Do nothing
      break;
  }
}

void Application::SetDecodeSampleRate(int sample_rate) {
//   if (opus_decode_sample_rate_ == sample_rate) {
//     return;
//   }

//   opus_decode_sample_rate_ = sample_rate;
//   opus_decoder_ =
//       std::make_unique<OpusDecoderWrapper>(opus_decode_sample_rate_, 1);

//   auto codec = Board::GetInstance().GetAudioCodec();
//   if (opus_decode_sample_rate_ != codec->output_sample_rate()) {
//     ESP_LOGI(TAG, "Resampling audio from %d to %d", opus_decode_sample_rate_,
//              codec->output_sample_rate());
//     output_resampler_.Configure(opus_decode_sample_rate_,
//                                 codec->output_sample_rate());
//   }
}

void Application::UpdateIotStates() {
  auto& thing_manager = iot::ThingManager::GetInstance();
  auto states = thing_manager.GetStatesJson();
  if (states != last_iot_states_) {
    last_iot_states_ = states;
    protocol_->SendIotStates(states);
  }
}

void Application::CloseAudioChannel() {
  if (protocol_ != nullptr) {
    protocol_->CloseAudioChannel();
  }

  // 发送完音频后，保持在 listening 状态，等待服务器响应
  // 不要切换到 idle 状态
  // SetDeviceState(kDeviceStateIdle);  // 删除这行

  // 添加超时检查
  background_task_->Schedule([this]() {
    vTaskDelay(pdMS_TO_TICKS(15000));  //  用于避免TTT逻辑延误
    std::lock_guard<std::mutex> lock(mutex_);
    if (device_state_ == kDeviceStateListening) {
      ESP_LOGW(TAG,
               "No response from server after 2 seconds, switching to idle");
      SetDeviceState(kDeviceStateIdle);
    }
  });
}

void Application::SaveLanguageTypeToNVS(const std::string& language) {
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open("config", NVS_READWRITE, &nvs_handle);
  if (err == ESP_OK) {
    err = nvs_set_str(nvs_handle, "languagesType", language.c_str());
    if (err != ESP_OK) {
      ESP_LOGE(TAG, "Failed to save languagesType to NVS");
    } else {
      ESP_LOGI(TAG, "languagesType saved to NVS: %s", language.c_str());
    }
    nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
  } else {
    ESP_LOGE(TAG, "Failed to open NVS for writing");
  }
}

void Application::SaveVolumeToNVS(int volume) {
  nvs_handle_t nvs_handle;
  if (nvs_open("config", NVS_READWRITE, &nvs_handle) == ESP_OK) {
    if (nvs_set_i32(nvs_handle, "volume", volume) != ESP_OK) {
      ESP_LOGW(TAG, "Failed to save volume to NVS");
    } else {
      ESP_LOGI(TAG, "Volume saved to NVS: %d", volume);
    }
    nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
  } else {
    ESP_LOGE(TAG, "Failed to open NVS for writing volume setting");
  }
}

int Application::LoadVolumeFromNVS(int default_value) {
  int volume = default_value;
  nvs_handle_t nvs_handle;

  if (nvs_open("config", NVS_READONLY, &nvs_handle) == ESP_OK) {
    int32_t saved_volume;  // Use int32_t to match nvs_get_i32 expectations
    if (nvs_get_i32(nvs_handle, "volume", &saved_volume) == ESP_OK) {
      // Ensure it's in valid range
      if (saved_volume >= 0 && saved_volume <= 100) {
        volume = saved_volume;
        ESP_LOGI(TAG, "Loaded saved volume: %d", volume);
      }
    }
    nvs_close(nvs_handle);
  }

  return volume;
}

// 从NVS获取当前语言设置，如果不存在则返回默认值
std::string Application::GetLanguageFromNVS() {
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open("storage", NVS_READONLY, &nvs_handle);
  if (err != ESP_OK) {
    ESP_LOGE(TAG, "Error opening NVS: %s", esp_err_to_name(err));
    return "en";  // 默认返回英语
  }

  // 首先获取需要的缓冲区大小
  size_t required_size = 0;
  err = nvs_get_str(nvs_handle, "language", nullptr, &required_size);
  if (err != ESP_OK && err != ESP_ERR_NVS_NOT_FOUND) {
    ESP_LOGE(TAG, "Error getting language size: %s", esp_err_to_name(err));
    nvs_close(nvs_handle);
    return "en";
  }

  // 如果找不到键，返回默认值
  if (err == ESP_ERR_NVS_NOT_FOUND) {
    nvs_close(nvs_handle);
    return "en";
  }

  // 分配缓冲区并读取值
  char* buffer = new char[required_size];
  err = nvs_get_str(nvs_handle, "language", buffer, &required_size);
  if (err != ESP_OK) {
    ESP_LOGE(TAG, "Error getting language: %s", esp_err_to_name(err));
    delete[] buffer;
    nvs_close(nvs_handle);
    return "en";
  }

  std::string language(buffer);
  delete[] buffer;
  nvs_close(nvs_handle);

  return language;
}

void Application::MonitorStopAndAudioQueue() {

    while (true) {
        vTaskDelay(pdMS_TO_TICKS(100));
        if ((mqtt_stop_flag_) && (device_state_ == kDeviceStateSpeaking)) {
            if (audio_decode_queue_->empty()) {

                auto now = std::chrono::steady_clock::now();
                auto ms_since_last_audio = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - last_audio_received_time_).count();

                if (ms_since_last_audio > 280) { // 280ms内没收到新音频才切换
                    ESP_LOGI(TAG, "Audio queue empty and no new audio for 280ms, switching to listening");
                    mqtt_stop_flag_ = false;
                    Schedule([this]() {

                        audio_decode_queue_->clear(); // 清空一次音频队列                        

                        //延时
                        vTaskDelay(pdMS_TO_TICKS(650));

                        SetDeviceState(kDeviceStateListening);
                        voice_detected_ = true;
                        silence_count_ = 10;
                    });
                }

            }
        }
    }


}


#if WIFI_SIGNAL_CHECK_TONE == 1  
void Application::MonitorWifiRssi() {

      uint8_t weak_count = 0;
      while (true) {
          vTaskDelay(pdMS_TO_TICKS(3000)); 
          wifi_mode_t mode; 
          esp_wifi_get_mode(&mode);
          wifi_ap_record_t ap_info;
          int8_t rssi = -127;

          if((mode == WIFI_MODE_STA)&&(esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK)){
              if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
                  rssi = ap_info.rssi;
              }
              if (rssi > -70) {
                  weak_count = 0;
                  continue;
              }
              weak_count++;
              if (weak_count >= 3) { 
                  weak_count = 0;

                  Schedule([this]() {
                      HandleWifiWeak(); 
                  });
              }
          }else{

          }

      }

  }

  void Application::HandleWifiWeak() {
    auto prev_state = device_state_;

      if (device_state_ == kDeviceStateSpeaking) {

          AbortSpeaking(kAbortReasonNone);
          protocol_->SendCancelTTS();
          audio_decode_queue_->clear();
      }


    SetDeviceState(kDeviceStateSpeaking);
    audio_decode_queue_->clear(); // 再次确保队列清空

    wifi_ap_record_t ap_info;
    bool wifi_connected = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);

    if (wifi_connected) {
        audio_decode_queue_->clear();
        // PlayOpusFile(p3_dingdingwifi_start, p3_dingdingwifi_end - p3_dingdingwifi_start);
        // vTaskDelay(pdMS_TO_TICKS(1000));
        PlayOpusFile(p3_wifi_weak_start, p3_wifi_weak_end - p3_wifi_weak_start);
        vTaskDelay(pdMS_TO_TICKS(2500));

    } 
        if (prev_state == kDeviceStateIdle) {
            SetDeviceState(kDeviceStateIdle); 
        } else {
            mqtt_stop_flag_ = false;
            SetDeviceState(kDeviceStateListening); 

            voice_detected_ = true;
            silence_count_ = 10;
        }
      
    
  }


#endif
