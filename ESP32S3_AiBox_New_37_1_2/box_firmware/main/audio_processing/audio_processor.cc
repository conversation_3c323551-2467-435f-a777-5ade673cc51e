/**
 * @file audio_processor.cc
 * @brief 音频处理器实现文件
 *
 * 该文件实现了AudioProcessor类的所有功能，包括ESP-SR库的配置、
 * 音频数据的实时处理、多线程管理等核心功能。
 *
 * 主要实现：
 * - ESP-SR库的初始化和配置
 * - AEC、NS、AGC算法的参数设置
 * - 音频数据的缓冲和批处理
 * - 高优先级音频处理线程
 * - 线程安全的状态管理
 *
 * <AUTHOR> AiBox项目组
 * @version 1.0
 * @date 2024
 */

#include "audio_processor.h"
#include <esp_log.h>

/// 音频处理器运行状态事件位
/// 用于控制音频处理线程的启动和停止
#define PROCESSOR_RUNNING 0x01

/// 日志标签，用于ESP_LOG系列函数的日志输出
static const char* TAG = "AudioProcessor";

/**
 * @brief AudioProcessor构造函数实现
 *
 * 初始化音频处理器的基本组件：
 * 1. 将ESP-SR数据指针初始化为nullptr
 * 2. 创建FreeRTOS事件组用于线程同步
 *
 * @note 此时不会创建音频处理线程，需要调用Initialize()
 */
AudioProcessor::AudioProcessor()
    : afe_communication_data_(nullptr) {
    // 创建事件组，用于控制音频处理线程的启动和停止
    event_group_ = xEventGroupCreate();
}

/**
 * @brief 初始化音频处理器
 *
 * 配置ESP-SR库的音频前端处理算法，包括AEC、NS、AGC等功能。
 * 根据输入参数设置合适的算法参数，创建音频处理线程。
 *
 * @param channels 音频输入通道数
 * @param reference 是否包含参考信号用于AEC
 */
void AudioProcessor::Initialize(int channels, bool reference) {
    // 保存配置参数
    channels_ = channels;
    reference_ = reference;

    // 计算参考信号通道数：有参考信号时为1，否则为0
    int ref_num = reference_ ? 1 : 0;

    /**
     * ESP-SR音频前端处理配置结构体
     * 该配置针对语音通信场景进行了优化
     */
    afe_config_t afe_config = {
        // === 算法模块开关配置 ===
        .aec_init = false,                    // AEC回声消除：关闭（在语音通信模块中处理）
        .se_init = true,                      // SE语音增强：启用，提高语音质量
        .vad_init = false,                    // VAD语音活动检测：关闭（由WakeWordDetect处理）
        .wakenet_init = false,                // 唤醒网络：关闭（由WakeWordDetect处理）
        .voice_communication_init = true,     // 语音通信模式：启用，专门优化通话质量
        .voice_communication_agc_init = true, // 语音通信AGC：启用自动增益控制

        // === AGC自动增益控制配置 ===
        .voice_communication_agc_gain = 10,   // AGC增益：10dB，适中的增益设置

        // === VAD语音活动检测配置 ===
        .vad_mode = VAD_MODE_4,              // VAD模式4：保持该值不做处理（注释说明）

        // === 唤醒词检测配置（未使用） ===
        .wakenet_model_name = NULL,           // 主唤醒词模型：未使用
        .wakenet_model_name_2 = NULL,         // 副唤醒词模型：未使用
        .wakenet_mode = DET_MODE_2CH_90,      // 唤醒检测模式：双通道90%置信度

        // === 系统性能配置 ===
        .afe_mode = SR_MODE_HIGH_PERF,        // AFE模式：高性能模式，优先处理质量
        .afe_perferred_core = 1,              // 首选CPU核心：核心1（避免与主任务冲突）
        .afe_perferred_priority = 1,          // 线程优先级：1（较低优先级，避免影响实时任务）
        .afe_ringbuf_size = 50,               // 环形缓冲区大小：50个数据块

        // === 内存分配策略 ===
        .memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM, // 优先使用PSRAM，节省内部RAM

        // === 音频增益配置 ===
        .afe_linear_gain = 1.0,               // 线性增益：1.0（无额外增益）
        .agc_mode = AFE_MN_PEAK_AGC_MODE_2,   // AGC模式：峰值检测模式2

        // === PCM音频配置 ===
        .pcm_config = {
            .total_ch_num = channels_,         // 总通道数：根据输入参数设置
            .mic_num = channels_ - ref_num,    // 麦克风通道数：总通道数减去参考通道数
            .ref_num = ref_num,                // 参考信号通道数：0或1
            .sample_rate = 16000,              // 采样率：16kHz（语音处理标准采样率）
        },

        // === 调试配置 ===
        .debug_init = false,                  // 调试模式：关闭
        .debug_hook = {                       // 调试钩子：未使用
            { AFE_DEBUG_HOOK_MASE_TASK_IN, NULL },
            { AFE_DEBUG_HOOK_FETCH_TASK_IN, NULL }
        },

        // === 噪声抑制配置 ===
        .afe_ns_mode = NS_MODE_SSP,           // NS模式：SSP（谱减法），经典的噪声抑制算法
        .afe_ns_model_name = NULL,            // NS模型名称：使用默认模型

        // === 通道配置 ===
        .fixed_first_channel = true,          // 固定第一通道：true，第一通道作为主麦克风
    };

    // === 创建ESP-SR音频前端处理实例 ===
    /**
     * 使用esp_afe_vc_v1接口创建音频前端处理实例
     * 该实例包含了所有配置的算法模块（AEC、NS、AGC等）
     * 创建后即可开始接收和处理音频数据
     */
    afe_communication_data_ = esp_afe_vc_v1.create_from_config(&afe_config);

    // === 创建音频处理线程 ===
    /**
     * 创建专用的音频处理线程
     *
     * 线程配置：
     * - 线程名称: "audio_communication"
     * - 堆栈大小: 8KB (4096 * 2)，足够处理音频算法的计算需求
     * - 优先级: 6（高优先级），确保音频处理的实时性
     * - 参数: this指针，用于访问类成员
     * - 任务句柄: NULL（不需要保存句柄）
     *
     * 使用lambda表达式作为任务函数，简化代码结构
     */
    xTaskCreate([](void* arg) {
        // 将void*参数转换为AudioProcessor指针
        auto this_ = (AudioProcessor*)arg;

        // 调用音频处理主循环
        this_->AudioProcessorTask();

        // 任务结束时删除自身（正常情况下不会执行到这里）
        vTaskDelete(NULL);
    }, "audio_communication", 4096 * 2, this, 6, NULL);
}

/**
 * @brief AudioProcessor析构函数实现
 *
 * 清理所有分配的资源，确保没有内存泄漏：
 * 1. 销毁ESP-SR库实例
 * 2. 删除FreeRTOS事件组
 *
 * @note 音频处理线程会在主循环中自动退出
 */
AudioProcessor::~AudioProcessor() {
    // 销毁ESP-SR音频前端处理实例，释放相关内存
    if (afe_communication_data_ != nullptr) {
        esp_afe_vc_v1.destroy(afe_communication_data_);
    }

    // 删除FreeRTOS事件组，释放系统资源
    vEventGroupDelete(event_group_);
}

/**
 * @brief 输入音频数据进行处理
 *
 * 将音频数据添加到内部缓冲区，当累积足够数据时，
 * 按ESP-SR库要求的块大小传递给算法进行处理。
 *
 * 处理流程：
 * 1. 将新数据追加到输入缓冲区
 * 2. 检查缓冲区是否有足够数据
 * 3. 按块大小传递给ESP-SR库
 * 4. 从缓冲区移除已处理的数据
 *
 * @param data 16位PCM音频数据，采样率16kHz
 *
 * @note 该函数线程安全，可在任意线程中调用
 * @performance 使用高效的vector操作，最小化内存拷贝
 */
void AudioProcessor::Input(const std::vector<int16_t>& data) {
    // === 将新数据追加到输入缓冲区 ===
    input_buffer_.insert(input_buffer_.end(), data.begin(), data.end());

    // === 获取ESP-SR库要求的数据块大小 ===
    // feed_size是ESP-SR库每次处理的音频样本数，乘以通道数得到总样本数
    auto feed_size = esp_afe_vc_v1.get_feed_chunksize(afe_communication_data_) * channels_;

    // === 批量处理音频数据 ===
    // 当缓冲区有足够数据时，循环处理所有完整的数据块
    while (input_buffer_.size() >= feed_size) {
        // 获取缓冲区数据指针，传递给ESP-SR库
        auto chunk = input_buffer_.data();

        // 将音频数据传递给ESP-SR库进行处理
        // 处理包括NS、AGC等算法
        esp_afe_vc_v1.feed(afe_communication_data_, chunk);

        // 从缓冲区移除已处理的数据，为新数据腾出空间
        input_buffer_.erase(input_buffer_.begin(), input_buffer_.begin() + feed_size);
    }
}

/**
 * @brief 启动音频处理器
 *
 * 设置PROCESSOR_RUNNING事件位，使音频处理线程开始工作。
 * 只有在启动状态下，输入的音频数据才会被实际处理。
 *
 * @thread_safety 线程安全，使用原子事件组操作
 */
void AudioProcessor::Start() {
    // 设置运行事件位，唤醒音频处理线程
    xEventGroupSetBits(event_group_, PROCESSOR_RUNNING);
}

/**
 * @brief 停止音频处理器
 *
 * 清除PROCESSOR_RUNNING事件位，使音频处理线程暂停工作。
 * 线程不会被销毁，可以通过Start()重新启动。
 *
 * @thread_safety 线程安全，使用原子事件组操作
 */
void AudioProcessor::Stop() {
    // 清除运行事件位，暂停音频处理线程
    xEventGroupClearBits(event_group_, PROCESSOR_RUNNING);
}

/**
 * @brief 检查音频处理器运行状态
 *
 * @return true 处理器正在运行
 * @return false 处理器已停止
 *
 * @thread_safety 线程安全，原子读取事件组状态
 */
bool AudioProcessor::IsRunning() {
    // 检查PROCESSOR_RUNNING事件位是否被设置
    return xEventGroupGetBits(event_group_) & PROCESSOR_RUNNING;
}

/**
 * @brief 设置音频输出回调函数
 *
 * 注册回调函数接收处理后的音频数据。
 * 回调函数在音频处理线程中执行，应避免耗时操作。
 *
 * @param callback 输出回调函数，使用移动语义避免数据拷贝
 *
 * @note 回调函数应尽快返回，避免影响音频处理的实时性
 */
void AudioProcessor::OnOutput(std::function<void(std::vector<int16_t>&& data)> callback) {
    // 保存回调函数，用于输出处理后的音频数据
    output_callback_ = callback;
}

/**
 * @brief 音频处理任务主循环
 *
 * 运行在独立线程中的音频处理主循环，负责从ESP-SR库获取
 * 处理后的音频数据并通过回调函数输出。
 *
 * 主要功能：
 * 1. 等待处理器启动信号
 * 2. 从ESP-SR库获取处理后的音频数据
 * 3. 错误处理和状态检查
 * 4. 通过回调函数输出处理结果
 *
 * 线程特性：
 * - 优先级: 6（高优先级）
 * - 堆栈: 8KB
 * - 实时性: 低延迟处理
 *
 * @note 该函数在独立线程中运行，包含无限循环
 */
void AudioProcessor::AudioProcessorTask() {
    // === 获取ESP-SR库的数据块大小信息 ===
    /**
     * fetch_size: ESP-SR库每次输出的音频样本数
     * feed_size: ESP-SR库每次输入的音频样本数
     * 这些参数由算法配置决定，用于优化处理性能
     */
    auto fetch_size = esp_afe_sr_v1.get_fetch_chunksize(afe_communication_data_);
    auto feed_size = esp_afe_sr_v1.get_feed_chunksize(afe_communication_data_);

    // 记录任务启动信息，用于调试和性能分析
    ESP_LOGI(TAG, "Audio communication task started, feed size: %d fetch size: %d",
        feed_size, fetch_size);

    // === 音频处理主循环 ===
    while (true) {
        // === 等待处理器启动信号 ===
        /**
         * 等待PROCESSOR_RUNNING事件位被设置
         * 参数说明：
         * - PROCESSOR_RUNNING: 等待的事件位
         * - pdFALSE: 等待后不自动清除事件位
         * - pdTRUE: 等待所有指定的事件位
         * - portMAX_DELAY: 无限等待
         */
        xEventGroupWaitBits(event_group_, PROCESSOR_RUNNING, pdFALSE, pdTRUE, portMAX_DELAY);

        // === 从ESP-SR库获取处理后的音频数据 ===
        /**
         * fetch()函数从ESP-SR库获取一帧处理后的音频数据
         * 返回的结果包含音频数据和处理状态信息
         */
        auto res = esp_afe_vc_v1.fetch(afe_communication_data_);

        // === 状态检查：确保处理器仍在运行状态 ===
        if ((xEventGroupGetBits(event_group_) & PROCESSOR_RUNNING) == 0) {
            // 处理器已停止，跳过本次处理
            continue;
        }

        // === 错误处理：检查ESP-SR库返回结果 ===
        if (res == nullptr || res->ret_value == ESP_FAIL) {
            // 处理失败，记录错误信息并继续下一次循环
            if (res != nullptr) {
                ESP_LOGI(TAG, "Error code: %d", res->ret_value);
            }
            continue;
        }

        // === 输出处理后的音频数据 ===
        if (output_callback_) {
            /**
             * 将ESP-SR库输出的音频数据转换为std::vector格式
             * 使用移动语义避免数据拷贝，提高性能
             *
             * 数据转换：
             * - res->data: ESP-SR库输出的原始数据指针
             * - res->data_size: 数据大小（字节）
             * - 除以sizeof(int16_t)得到样本数
             */
            output_callback_(std::vector<int16_t>(res->data, res->data + res->data_size / sizeof(int16_t)));
        }
    }
}
