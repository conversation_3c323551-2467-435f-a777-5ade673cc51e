/**
 * @file wake_word_detect.h
 * @brief 语音唤醒检测头文件
 *
 * 该文件定义了WakeWordDetect类，负责实现离线语音唤醒检测功能。
 * 基于ESP-SR（Espressif Speech Recognition）库，提供高精度的
 * 本地语音唤醒检测，同时集成VAD（Voice Activity Detection）
 * 语音活动检测功能。
 *
 * 主要功能：
 * - 离线语音唤醒检测：支持多种唤醒词，无需网络连接
 * - VAD语音活动检测：实时检测语音活动状态
 * - 能量阈值检测：结合能量分析提高检测准确性
 * - 状态平滑算法：减少误触发和漏检
 * - 唤醒词音频编码：保存唤醒前的音频数据
 * - 多线程实时处理：确保检测的低延迟和高可靠性
 *
 * 技术特性：
 * - 基于深度学习的唤醒词检测模型
 * - 支持自定义唤醒词和检测阈值
 * - 低功耗设计，适合长时间运行
 * - 抗噪声干扰，适应复杂声学环境
 * - 线程安全的接口设计
 *
 * <AUTHOR> AiBox项目组
 * @version 1.0
 * @date 2024
 */

#ifndef WAKE_WORD_DETECT_H
#define WAKE_WORD_DETECT_H

// FreeRTOS相关头文件，用于多线程和同步
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/event_groups.h>

// ESP-SR库头文件，提供语音识别和唤醒检测算法
#include <esp_afe_sr_models.h>  // 音频前端处理模型
#include <esp_nsn_models.h>     // 神经网络语音模型

// C++标准库头文件
#include <list>                 // 用于音频数据队列管理
#include <string>               // 用于唤醒词字符串处理
#include <vector>               // 用于音频数据缓冲
#include <functional>           // 用于回调函数
#include <mutex>                // 用于线程同步
#include <condition_variable>   // 用于线程间通信


/**
 * @class WakeWordDetect
 * @brief 语音唤醒检测类
 *
 * 该类实现了基于ESP-SR库的离线语音唤醒检测功能，支持多种唤醒词
 * 的实时检测。同时集成了VAD语音活动检测，提供完整的语音前端
 * 处理能力。
 *
 * 核心功能：
 * - 离线唤醒词检测：基于深度学习模型，支持自定义唤醒词
 * - VAD语音活动检测：实时检测语音/静音状态
 * - 能量阈值分析：结合音频能量提高检测准确性
 * - 状态平滑处理：使用滑动窗口减少误触发
 * - 音频数据缓存：保存唤醒前的音频用于后续处理
 * - OPUS编码支持：将缓存的音频编码为OPUS格式
 *
 * 技术特点：
 * - 低延迟：检测延迟<500ms
 * - 高准确率：结合多种检测算法
 * - 低功耗：优化的算法和硬件加速
 * - 抗干扰：适应噪声环境和回声
 * - 线程安全：支持多线程并发访问
 *
 * 使用场景：
 * - 智能音箱语音唤醒
 * - 语音助手激活
 * - 免提通话启动
 * - 语音控制设备
 *
 * @note 需要ESP32-S3芯片和ESP-SR库支持
 * @warning 检测线程具有较高优先级，避免在回调中执行耗时操作
 */
class WakeWordDetect {
public:
    /**
     * @brief 构造函数
     *
     * 初始化语音唤醒检测器的基本组件，创建事件组用于线程同步。
     * 此时不会启动实际的检测，需要调用Initialize()进行配置。
     */
    WakeWordDetect();

    /**
     * @brief 析构函数
     *
     * 清理ESP-SR库资源，删除事件组和线程，确保资源正确释放。
     * 自动停止检测线程并清理相关资源。
     */
    ~WakeWordDetect();

    /**
     * @brief 初始化语音唤醒检测器
     *
     * 配置ESP-SR库的唤醒检测参数，加载唤醒词模型，创建检测线程。
     * 该函数会根据输入参数配置VAD、唤醒检测等算法的工作模式。
     *
     * @param channels 音频输入通道数（1=单声道，2=双声道）
     * @param reference 是否包含参考信号（用于回声抑制）
     *                  true: 双声道输入，其中一路为参考信号
     *                  false: 纯麦克风输入，无参考信号
     *
     * @note 必须在使用其他功能前调用此函数
     * @note 参考信号有助于提高在播放音频时的检测准确性
     */
    void Initialize(int channels, bool reference);

    /**
     * @brief 输入音频数据进行检测
     *
     * 将原始音频数据输入到检测器进行实时分析。
     * 数据会被缓存并按ESP-SR库要求的块大小进行处理。
     *
     * @param data 16位PCM音频数据，采样率16kHz
     *             单声道：[sample0, sample1, ...]
     *             双声道：[L0, R0, L1, R1, ...]（交错格式）
     *
     * @note 该函数线程安全，可在任意线程中调用
     * @note 输入数据会被复制到内部缓冲区，调用者可安全释放原数据
     * @performance 使用高效的缓冲区管理，避免频繁内存分配
     */
    void Feed(const std::vector<int16_t>& data);

    /**
     * @brief 设置唤醒词检测回调函数
     *
     * 注册回调函数接收唤醒词检测结果。当检测到配置的唤醒词时，
     * 会调用此回调函数并传递检测到的唤醒词字符串。
     *
     * @param callback 唤醒词检测回调函数
     *                 回调函数签名：void(const std::string& wake_word)
     *                 参数为检测到的唤醒词字符串
     *
     * @note 回调函数在检测线程中执行，应避免耗时操作
     * @note 建议在回调中将事件转发到主线程进行处理
     * @warning 回调函数中不应调用阻塞操作或长时间计算
     */
    void OnWakeWordDetected(std::function<void(const std::string& wake_word)> callback);

    /**
     * @brief 设置VAD状态变化回调函数
     *
     * 注册回调函数接收语音活动检测状态变化。当检测到语音开始
     * 或结束时，会调用此回调函数并传递当前的语音状态。
     *
     * @param callback VAD状态变化回调函数
     *                 回调函数签名：void(bool speaking)
     *                 参数：true=检测到语音，false=静音状态
     *
     * @note 回调函数在检测线程中执行，应避免耗时操作
     * @note VAD状态用于UI更新和语音处理流程控制
     * @warning 回调函数中不应调用阻塞操作或长时间计算
     */
    void OnVadStateChange(std::function<void(bool speaking)> callback);

    /**
     * @brief 启动语音唤醒检测
     *
     * 开始语音唤醒检测线程的工作，使检测器进入活跃状态。
     * 只有在启动状态下，输入的音频数据才会被实际检测。
     *
     * @note 可以多次调用，重复调用无副作用
     * @thread_safety 线程安全，使用事件组同步
     */
    void StartDetection();

    /**
     * @brief 停止语音唤醒检测
     *
     * 暂停语音唤醒检测线程的工作，但不销毁线程和资源。
     * 可以通过StartDetection()重新启动检测。
     *
     * @note 停止后输入的音频数据会被缓存但不检测
     * @thread_safety 线程安全，使用事件组同步
     */
    void StopDetection();

    /**
     * @brief 检查检测器运行状态
     *
     * @return true 检测器正在运行
     * @return false 检测器已停止
     *
     * @thread_safety 线程安全，原子操作
     */
    bool IsDetectionRunning();

    /**
     * @brief 编码唤醒词音频数据
     *
     * 将缓存的唤醒前音频数据编码为OPUS格式，用于发送到服务器
     * 进行进一步的语音识别处理。编码过程在独立线程中进行。
     *
     * @note 该函数会创建临时线程进行OPUS编码
     * @note 编码完成后可通过GetWakeWordOpus()获取结果
     * @performance 使用专用线程避免阻塞主流程
     */
    void EncodeWakeWordData();

    /**
     * @brief 获取编码后的唤醒词音频数据
     *
     * 获取OPUS编码后的唤醒词音频数据包。该函数会阻塞等待
     * 直到有可用的编码数据或编码完成。
     *
     * @param opus 输出参数，接收OPUS编码的音频数据
     * @return true 成功获取到音频数据
     * @return false 编码已完成，无更多数据
     *
     * @note 该函数会阻塞等待，直到有数据可用
     * @thread_safety 线程安全，使用条件变量同步
     */
    bool GetWakeWordOpus(std::vector<uint8_t>& opus);

    /**
     * @brief 获取最后检测到的唤醒词
     *
     * @return const std::string& 最后检测到的唤醒词字符串
     *
     * @note 如果从未检测到唤醒词，返回空字符串
     * @thread_safety 线程安全，只读访问
     */
    const std::string& GetLastDetectedWakeWord() const { return last_detected_wake_word_; }

private:
    // === ESP-SR库相关成员 ===

    /// ESP-SR库的音频前端检测数据结构指针
    /// 包含唤醒检测、VAD等算法的内部状态和配置
    esp_afe_sr_data_t* afe_detection_data_ = nullptr;

    /// 唤醒网络模型指针，指向加载的唤醒词检测模型
    /// 由ESP-SR库管理，包含深度学习模型参数
    char* wakenet_model_ = NULL;

    /// 支持的唤醒词列表，存储所有可检测的唤醒词字符串
    /// 与模型中的唤醒词索引对应
    std::vector<std::string> wake_words_;

    // === 音频数据处理相关 ===

    /// 音频输入缓冲区，存储待检测的音频数据
    /// 当累积足够数据时，按块传递给ESP-SR库检测
    std::vector<int16_t> input_buffer_;

    /// FreeRTOS事件组句柄，用于线程同步和状态控制
    /// 控制音频检测线程的启动、停止和运行状态
    EventGroupHandle_t event_group_;

    // === 回调函数相关 ===

    /// 唤醒词检测回调函数，当检测到唤醒词时调用
    /// 传递检测到的唤醒词字符串给上层应用
    std::function<void(const std::string& wake_word)> wake_word_detected_callback_;

    /// VAD状态变化回调函数，当语音活动状态改变时调用
    /// 传递当前的语音状态（true=语音，false=静音）
    std::function<void(bool speaking)> vad_state_change_callback_;

    /// VAD切片变化回调函数（预留接口，当前未使用）
    /// 用于更细粒度的VAD状态通知
    std::function<void(void )> vad_slice_change_callback_;

    // === 状态管理相关 ===

    /// 当前语音状态标志
    /// true: 正在说话，false: 静音状态
    bool is_speaking_ = false;

    /// 音频输入通道数（1=单声道，2=双声道）
    /// 影响ESP-SR库的配置和数据处理方式
    int channels_;

    /// 是否包含参考信号用于回声抑制
    /// true: 输入包含扬声器参考信号，false: 纯麦克风输入
    bool reference_;

    /// 最后检测到的唤醒词字符串
    /// 保存最近一次成功检测的唤醒词，用于查询和调试
    std::string last_detected_wake_word_;

    // === OPUS编码任务相关 ===

    /// OPUS编码任务句柄，用于管理编码线程
    /// 编码线程负责将PCM音频数据转换为OPUS格式
    TaskHandle_t wake_word_encode_task_ = nullptr;

    /// 静态任务控制块，用于创建静态任务
    /// 提供更好的内存管理和性能
    StaticTask_t wake_word_encode_task_buffer_;

    /// 编码任务堆栈指针，指向分配的堆栈内存
    /// 使用PSRAM分配大容量堆栈
    StackType_t* wake_word_encode_task_stack_ = nullptr;

    // === 音频数据缓存相关 ===

    /// 唤醒词PCM音频数据队列，存储唤醒前的音频数据
    /// 每个元素是一个音频帧，用于后续的OPUS编码
    std::list<std::vector<int16_t>> wake_word_pcm_;

    /// 唤醒词OPUS编码数据队列，存储编码后的音频数据
    /// 每个元素是一个OPUS数据包，用于网络传输
    std::list<std::vector<uint8_t>> wake_word_opus_;

    /// 唤醒词数据访问互斥锁，保护音频数据队列的线程安全
    /// 用于同步编码线程和主线程对数据的访问
    std::mutex wake_word_mutex_;

    /// 唤醒词数据条件变量，用于线程间通信
    /// 通知等待线程有新的编码数据可用
    std::condition_variable wake_word_cv_;

    // === VAD状态平滑相关 ===

    /// VAD状态连续性检查数组，用于状态平滑
    /// 存储最近几帧的VAD检测结果，减少误触发
    std::vector<bool> continue_state_;

    /// VAD状态平滑窗口大小，默认为3帧
    /// 用于控制状态平滑的敏感度
    int continue_state_num_ = 3;

    /// 上一次的连续状态结果，用于状态变化检测
    /// 避免重复触发相同的状态变化事件
    bool last_continue_state_ = false;

    // === 私有方法 ===

    /**
     * @brief 存储唤醒词音频数据
     *
     * 将音频数据存储到PCM缓冲区，用于后续的OPUS编码。
     * 维护约2秒的音频数据，超出部分会被自动删除。
     *
     * @param data 音频数据指针
     * @param size 音频数据样本数
     *
     * @note 该函数在检测线程中调用
     * @performance 使用循环缓冲区策略，控制内存使用
     */
    void StoreWakeWordData(uint16_t* data, size_t size);

    /**
     * @brief 音频检测任务主循环
     *
     * 运行在独立线程中的音频检测主循环，负责：
     * 1. 等待检测器启动事件
     * 2. 从ESP-SR库获取检测结果
     * 3. 执行VAD状态分析和平滑处理
     * 4. 触发唤醒词和VAD状态回调
     * 5. 存储唤醒前的音频数据
     *
     * 线程特性：
     * - 线程名称: "audio_detection"
     * - 优先级: 4（中高优先级）
     * - 堆栈大小: 8KB
     *
     * @note 该函数在独立线程中运行，包含无限循环
     */
    void AudioDetectionTask();
};

#endif
