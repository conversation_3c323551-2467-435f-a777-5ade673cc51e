/**
 * @file wake_word_detect.cc
 * @brief 语音唤醒检测实现文件
 *
 * 该文件实现了WakeWordDetect类的所有功能，包括ESP-SR库的配置、
 * 唤醒词模型加载、VAD检测、音频数据处理、OPUS编码等核心功能。
 *
 * 主要实现：
 * - ESP-SR库的初始化和唤醒词模型加载
 * - 实时音频检测和VAD状态分析
 * - 能量阈值检测和状态平滑算法
 * - 唤醒前音频数据的缓存和编码
 * - 高优先级检测线程管理
 * - 线程安全的状态管理和数据访问
 *
 * 算法特点：
 * - 双重检测机制：ESP-SR VAD + 能量阈值
 * - 状态平滑：滑动窗口减少误触发
 * - 音频缓存：保存2秒唤醒前音频
 * - OPUS编码：高效的音频压缩
 *
 * <AUTHOR> AiBox项目组
 * @version 1.0
 * @date 2024
 */

#include "wake_word_detect.h"
#include "application.h"
#include <algorithm>          // 用于STL算法函数
#include <esp_log.h>          // ESP32日志系统
#include <model_path.h>       // 模型路径定义
#include <arpa/inet.h>        // 网络字节序转换
#include <sstream>            // 字符串流处理

/// 语音检测运行状态事件位
/// 用于控制音频检测线程的启动和停止
#define DETECTION_RUNNING_EVENT 1

/// 日志标签，用于ESP_LOG系列函数的日志输出
static const char* TAG = "WakeWordDetect";

/**
 * @brief WakeWordDetect构造函数实现
 *
 * 初始化语音唤醒检测器的基本组件：
 * 1. 将ESP-SR数据指针初始化为nullptr
 * 2. 初始化音频数据队列
 * 3. 创建VAD状态平滑数组
 * 4. 创建FreeRTOS事件组用于线程同步
 *
 * @note 此时不会创建检测线程，需要调用Initialize()
 */
WakeWordDetect::WakeWordDetect()
    : afe_detection_data_(nullptr),      // ESP-SR检测数据结构初始化为空
      wake_word_pcm_(),                  // PCM音频数据队列初始化
      wake_word_opus_() {                // OPUS编码数据队列初始化

  // 初始化VAD状态平滑数组，默认大小为3，全部初始化为false
  continue_state_ = std::vector<bool>(continue_state_num_, false);

  // 创建事件组，用于控制音频检测线程的启动和停止
  event_group_ = xEventGroupCreate();
}

/**
 * @brief WakeWordDetect析构函数实现
 *
 * 清理所有分配的资源，确保没有内存泄漏：
 * 1. 销毁ESP-SR库实例
 * 2. 释放编码任务堆栈内存
 * 3. 删除FreeRTOS事件组
 *
 * @note 音频检测线程会在主循环中自动退出
 */
WakeWordDetect::~WakeWordDetect() {
    // 销毁ESP-SR音频前端检测实例，释放相关内存
    if (afe_detection_data_ != nullptr) {
        esp_afe_sr_v1.destroy(afe_detection_data_);
    }

    // 释放OPUS编码任务堆栈内存（如果已分配）
    if (wake_word_encode_task_stack_ != nullptr) {
        // 使用heap_caps_free释放PSRAM内存
        heap_caps_free(wake_word_encode_task_stack_);
    }

    // 删除FreeRTOS事件组，释放系统资源
    vEventGroupDelete(event_group_);
}

/**
 * @brief 初始化语音唤醒检测器
 *
 * 配置ESP-SR库的唤醒检测参数，加载唤醒词模型，创建检测线程。
 * 该函数完成以下初始化工作：
 * 1. 加载并解析唤醒词模型
 * 2. 配置ESP-SR库参数
 * 3. 创建音频检测线程
 *
 * @param channels 音频输入通道数
 * @param reference 是否包含参考信号用于回声抑制
 */
void WakeWordDetect::Initialize(int channels, bool reference) {
    // 保存配置参数
    channels_ = channels;
    reference_ = reference;

    // 计算参考信号通道数：有参考信号时为1，否则为0
    int ref_num = reference_ ? 1 : 0;

    // === 加载唤醒词模型 ===
    /**
     * 初始化ESP-SR模型列表，从"model"目录加载所有可用模型
     * 包括唤醒词模型、命令词模型等
     */
    srmodel_list_t *models = esp_srmodel_init("model");

    // 遍历所有可用模型，查找唤醒词模型
    for (int i = 0; i < models->num; i++) {
        // 记录所有可用模型，用于调试
        ESP_LOGI(TAG, "Model %d: %s", i, models->model_name[i]);

        // 检查是否是唤醒词模型（前缀为ESP_WN_PREFIX）
        if (strstr(models->model_name[i], ESP_WN_PREFIX) != NULL) {
            // 保存唤醒词模型名称
            wakenet_model_ = models->model_name[i];

            // 获取该模型支持的所有唤醒词
            auto words = esp_srmodel_get_wake_words(models, wakenet_model_);

            // 解析唤醒词字符串，以分号分隔的多个唤醒词
            std::stringstream ss(words);
            std::string word;
            while (std::getline(ss, word, ';')) {
                // 将每个唤醒词添加到支持列表中
                wake_words_.push_back(word);
            }
        }
    }

    // === 配置ESP-SR音频前端处理参数 ===
    /**
     * ESP-SR音频前端检测配置结构体
     * 该配置专门针对唤醒词检测和VAD功能进行了优化
     */
    afe_config_t afe_config = {
        // === 算法模块开关配置 ===
        .aec_init = true,                     // AEC回声消除：启用，提高检测准确性
        .se_init = true,                      // SE语音增强：启用，改善音频质量
        .vad_init = true,                     // VAD语音活动检测：启用，核心功能
        .wakenet_init = true,                 // 唤醒网络：启用，核心功能
        .voice_communication_init = false,    // 语音通信模式：关闭（由AudioProcessor处理）
        .voice_communication_agc_init = false, // 语音通信AGC：关闭

        // === AGC自动增益控制配置 ===
        .voice_communication_agc_gain = 15,   // AGC增益：15dB（未使用，因为AGC已关闭）

        // === VAD语音活动检测配置 ===
        .vad_mode = VAD_MODE_3,               // VAD模式3：平衡灵敏度和误报率

        // === 唤醒词检测配置 ===
        .wakenet_model_name = wakenet_model_, // 主唤醒词模型：使用加载的模型
        .wakenet_model_name_2 = NULL,         // 副唤醒词模型：未使用
        .wakenet_mode = DET_MODE_2CH_95,      // 唤醒检测模式：双通道95%置信度

        // === 系统性能配置 ===
        .afe_mode = SR_MODE_HIGH_PERF,        // AFE模式：高性能模式，优先检测准确性
        .afe_perferred_core = 1,              // 首选CPU核心：核心1（避免与主任务冲突）
        .afe_perferred_priority = 1,          // 线程优先级：1（较低优先级，避免影响实时任务）
        .afe_ringbuf_size = 50,               // 环形缓冲区大小：50个数据块

        // === 内存分配策略 ===
        .memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM, // 优先使用PSRAM，节省内部RAM

        // === 音频增益配置 ===
        .afe_linear_gain = 1.0,               // 线性增益：1.0（无额外增益）
        .agc_mode = AFE_MN_PEAK_AGC_MODE_2,   // AGC模式：峰值检测模式2

        // === PCM音频配置 ===
        .pcm_config = {
            .total_ch_num = channels_,         // 总通道数：根据输入参数设置
            .mic_num = channels_ - ref_num,    // 麦克风通道数：总通道数减去参考通道数
            .ref_num = ref_num,                // 参考信号通道数：0或1
            .sample_rate = 16000               // 采样率：16kHz（语音处理标准采样率）
        },

        // === 调试配置 ===
        .debug_init = false,                  // 调试模式：关闭
        .debug_hook = {                       // 调试钩子：未使用
            { AFE_DEBUG_HOOK_MASE_TASK_IN, NULL },
            { AFE_DEBUG_HOOK_FETCH_TASK_IN, NULL }
        },

        // === 噪声抑制配置 ===
        .afe_ns_mode = NS_MODE_SSP,           // NS模式：SSP（谱减法），经典的噪声抑制算法
        .afe_ns_model_name = NULL,            // NS模型名称：使用默认模型

        // === 通道配置 ===
        .fixed_first_channel = true,          // 固定第一通道：true，第一通道作为主麦克风
    };

    // === 创建ESP-SR音频前端检测实例 ===
    /**
     * 使用esp_afe_sr_v1接口创建音频前端检测实例
     * 该实例包含了所有配置的算法模块（AEC、VAD、唤醒检测等）
     * 创建后即可开始接收和处理音频数据
     */
    afe_detection_data_ = esp_afe_sr_v1.create_from_config(&afe_config);

    // === 创建音频检测线程 ===
    /**
     * 创建专用的音频检测线程
     *
     * 线程配置：
     * - 线程名称: "audio_detection"
     * - 堆栈大小: 8KB (4096 * 2)，足够处理检测算法的计算需求
     * - 优先级: 4（中高优先级），确保检测的实时性
     * - 参数: this指针，用于访问类成员
     * - 任务句柄: nullptr（不需要保存句柄）
     *
     * 使用lambda表达式作为任务函数，简化代码结构
     */
    xTaskCreate([](void* arg) {
        // 将void*参数转换为WakeWordDetect指针
        auto this_ = (WakeWordDetect*)arg;

        // 调用音频检测主循环
        this_->AudioDetectionTask();

        // 任务结束时删除自身（正常情况下不会执行到这里）
        vTaskDelete(NULL);
    }, "audio_detection", 4096 * 2, this, 4, nullptr);
}

/**
 * @brief 设置唤醒词检测回调函数
 *
 * 注册回调函数接收唤醒词检测结果。当检测到配置的唤醒词时，
 * 会调用此回调函数并传递检测到的唤醒词字符串。
 *
 * @param callback 唤醒词检测回调函数
 *
 * @note 回调函数在检测线程中执行，应避免耗时操作
 */
void WakeWordDetect::OnWakeWordDetected(std::function<void(const std::string& wake_word)> callback) {
    // 保存唤醒词检测回调函数
    wake_word_detected_callback_ = callback;
}

/**
 * @brief 设置VAD状态变化回调函数
 *
 * 注册回调函数接收语音活动检测状态变化。当检测到语音开始
 * 或结束时，会调用此回调函数并传递当前的语音状态。
 *
 * @param callback VAD状态变化回调函数
 *
 * @note 回调函数在检测线程中执行，应避免耗时操作
 */
void WakeWordDetect::OnVadStateChange(std::function<void(bool speaking)> callback) {
    // 保存VAD状态变化回调函数
    vad_state_change_callback_ = callback;
}

/**
 * @brief 启动语音唤醒检测
 *
 * 设置DETECTION_RUNNING_EVENT事件位，使音频检测线程开始工作。
 * 只有在启动状态下，输入的音频数据才会被实际检测。
 *
 * @thread_safety 线程安全，使用原子事件组操作
 */
void WakeWordDetect::StartDetection() {
    // 设置运行事件位，唤醒音频检测线程
    xEventGroupSetBits(event_group_, DETECTION_RUNNING_EVENT);
}

/**
 * @brief 停止语音唤醒检测
 *
 * 清除DETECTION_RUNNING_EVENT事件位，使音频检测线程暂停工作。
 * 线程不会被销毁，可以通过StartDetection()重新启动。
 *
 * @thread_safety 线程安全，使用原子事件组操作
 */
void WakeWordDetect::StopDetection() {
    // 清除运行事件位，暂停音频检测线程
    xEventGroupClearBits(event_group_, DETECTION_RUNNING_EVENT);
}

/**
 * @brief 检查语音唤醒检测器运行状态
 *
 * @return true 检测器正在运行
 * @return false 检测器已停止
 *
 * @thread_safety 线程安全，原子读取事件组状态
 */
bool WakeWordDetect::IsDetectionRunning() {
    // 检查DETECTION_RUNNING_EVENT事件位是否被设置
    return xEventGroupGetBits(event_group_) & DETECTION_RUNNING_EVENT;
}

/**
 * @brief 输入音频数据进行检测
 *
 * 将音频数据添加到内部缓冲区，当累积足够数据时，
 * 按ESP-SR库要求的块大小传递给算法进行检测。
 *
 * 处理流程：
 * 1. 将新数据追加到输入缓冲区
 * 2. 检查缓冲区是否有足够数据
 * 3. 按块大小传递给ESP-SR库
 * 4. 从缓冲区移除已处理的数据
 *
 * @param data 16位PCM音频数据，采样率16kHz
 *
 * @note 该函数线程安全，可在任意线程中调用
 * @performance 使用高效的vector操作，最小化内存拷贝
 */
void WakeWordDetect::Feed(const std::vector<int16_t>& data) {
    // === 将新数据追加到输入缓冲区 ===
    input_buffer_.insert(input_buffer_.end(), data.begin(), data.end());

    // === 获取ESP-SR库要求的数据块大小 ===
    // feed_size是ESP-SR库每次处理的音频样本数，乘以通道数得到总样本数
    auto feed_size = esp_afe_sr_v1.get_feed_chunksize(afe_detection_data_) * channels_;

    // === 批量处理音频数据 ===
    // 当缓冲区有足够数据时，循环处理所有完整的数据块
    while (input_buffer_.size() >= feed_size) {
        // 将音频数据传递给ESP-SR库进行检测
        // 处理包括VAD检测、唤醒词检测等算法
        esp_afe_sr_v1.feed(afe_detection_data_, input_buffer_.data());

        // 从缓冲区移除已处理的数据，为新数据腾出空间
        input_buffer_.erase(input_buffer_.begin(), input_buffer_.begin() + feed_size);
    }
}

/**
 * @brief 音频检测任务主循环
 *
 * 运行在独立线程中的音频检测主循环，负责从ESP-SR库获取
 * 检测结果并进行VAD状态分析、唤醒词检测等处理。
 *
 * 主要功能：
 * 1. 等待检测器启动信号
 * 2. 从ESP-SR库获取检测结果
 * 3. 计算音频能量阈值
 * 4. 执行VAD状态平滑处理
 * 5. 检测唤醒词并触发回调
 * 6. 存储唤醒前的音频数据
 *
 * 线程特性：
 * - 优先级: 4（中高优先级）
 * - 堆栈: 8KB
 * - 实时性: 低延迟检测
 *
 * @note 该函数在独立线程中运行，包含无限循环
 */
void WakeWordDetect::AudioDetectionTask() {
    // === 获取ESP-SR库的数据块大小信息 ===
    /**
     * fetch_size: ESP-SR库每次输出的检测结果数据大小
     * feed_size: ESP-SR库每次输入的音频样本数
     * 这些参数由算法配置决定，用于优化处理性能
     */
    auto fetch_size = esp_afe_sr_v1.get_fetch_chunksize(afe_detection_data_);
    auto feed_size = esp_afe_sr_v1.get_feed_chunksize(afe_detection_data_);

    // 记录任务启动信息，用于调试和性能分析
    ESP_LOGI(TAG, "Audio detection task started, feed size: %d fetch size: %d",
        feed_size, fetch_size);

    // === 静音检测相关变量（当前未使用） ===
    int silence_count = 0;                    // 静音帧计数器
    const int SILENCE_THRESHOLD = 2;          // 静音阈值

    // === 音频检测主循环 ===
    while (true) {
        // === 等待检测器启动信号 ===
        /**
         * 等待DETECTION_RUNNING_EVENT事件位被设置
         * 参数说明：
         * - DETECTION_RUNNING_EVENT: 等待的事件位
         * - pdFALSE: 等待后不自动清除事件位
         * - pdTRUE: 等待所有指定的事件位
         * - portMAX_DELAY: 无限等待
         */
        xEventGroupWaitBits(event_group_, DETECTION_RUNNING_EVENT, pdFALSE, pdTRUE, portMAX_DELAY);

        // === 从ESP-SR库获取检测结果 ===
        /**
         * fetch()函数从ESP-SR库获取一帧检测结果
         * 返回的结果包含VAD状态、唤醒词检测结果、音频数据等
         */
        auto res = esp_afe_sr_v1.fetch(afe_detection_data_);

        // === 错误处理：检查ESP-SR库返回结果 ===
        if (res == nullptr || res->ret_value == ESP_FAIL) {
            // 检测失败，记录错误信息并继续下一次循环
            if (res != nullptr) {
                ESP_LOGI(TAG, "Error code: %d", res->ret_value);
            }
            continue;
        }

        // === 音频能量阈值检测 ===
        /**
         * 计算当前音频帧的能量值，用于辅助VAD检测
         * 能量计算公式：E = Σ(sample²) / N
         * 其中sample是音频样本值，N是样本总数
         */
        float energy = 0.0f;
        if (res->data && res->data_size > 0) {
            // 将音频数据转换为16位PCM格式
            const int16_t* pcm = reinterpret_cast<const int16_t*>(res->data);
            size_t samples = res->data_size / sizeof(int16_t);

            // 计算所有样本的平方和
            float sum = 0.0f;
            for (size_t i = 0; i < samples; ++i) {
                sum += pcm[i] * pcm[i];
            }

            // 计算平均能量值
            energy = sum / samples;
        }

        // === 双重VAD检测机制 ===
        /**
         * 结合ESP-SR VAD检测结果和能量阈值进行综合判断
         * - ESP-SR VAD: 基于深度学习的语音活动检测
         * - 能量阈值: 31210，过滤低能量噪声
         * 只有两个条件都满足才认为是有效语音
         */
        const float ENERGY_THRESHOLD = 31210;
        bool is_speech = (res->vad_state == AFE_VAD_SPEECH) && (energy > ENERGY_THRESHOLD);


        // === VAD状态处理和平滑算法 ===
        if (vad_state_change_callback_) {
            // 重置静音计数器（当前未使用）
            silence_count = 0;

            // === VAD状态滑动窗口处理 ===
            /**
             * 将当前帧的语音检测结果添加到滑动窗口中
             * 滑动窗口用于平滑VAD检测结果，减少误触发
             */
            continue_state_.push_back(is_speech);

            // 维护滑动窗口大小，移除最旧的检测结果
            if (continue_state_.size() > continue_state_num_) {
                continue_state_.erase(continue_state_.begin());
            }

            // === VAD状态平滑算法 ===
            /**
             * 使用复杂的逻辑组合来平滑VAD状态变化
             *
             * add_state: 窗口内所有帧都是语音（严格条件）
             * or_state: 窗口内至少有一帧是语音（宽松条件）
             * xor_state: 严格条件和宽松条件的异或（中间状态）
             * cur_state: 当前帧语音状态与中间状态的组合
             */
            bool add_state = std::count(continue_state_.begin(), continue_state_.end(), true) == continue_state_num_;
            bool or_state = std::count(continue_state_.begin(), continue_state_.end(), true) != 0;
            bool xor_state = add_state ^ or_state;
            bool cur_state = is_speech & (!xor_state);

            // === 最终VAD状态计算 ===
            /**
             * 结合历史状态和当前状态计算最终的VAD状态
             * 这种算法可以有效减少状态抖动，提供稳定的VAD输出
             */
            last_continue_state_ = (last_continue_state_ & xor_state) | cur_state;

            // 通过回调函数通知上层应用VAD状态变化
            vad_state_change_callback_(last_continue_state_);
        }

        // === 唤醒词检测处理 ===
        if (res->wakeup_state == WAKENET_DETECTED) {
            /**
             * 检测到唤醒词时的处理流程：
             * 1. 停止检测，避免重复触发
             * 2. 记录检测到的唤醒词
             * 3. 重置VAD状态
             * 4. 触发唤醒词检测回调
             */

            // 停止检测，避免重复触发唤醒词
            StopDetection();

            // 根据唤醒词索引获取对应的唤醒词字符串
            // 注意：索引从1开始，所以需要减1
            last_detected_wake_word_ = wake_words_[res->wake_word_index - 1];

            if (wake_word_detected_callback_) {
                // 强制设置VAD状态为语音状态（唤醒时必然有语音）
                vad_state_change_callback_(1);

                // 重置VAD状态平滑窗口，清除历史状态
                continue_state_ = std::vector<bool>(continue_state_num_, false);
                last_continue_state_ = false;

                // 触发唤醒词检测回调，通知上层应用
                wake_word_detected_callback_(last_detected_wake_word_);
            }
        }
    }
}


/**
 * @brief 存储唤醒词音频数据
 *
 * 将音频数据存储到PCM缓冲区，用于后续的OPUS编码。
 * 维护约2秒的音频数据，超出部分会被自动删除。
 *
 * 缓存策略：
 * - 检测周期：32ms（采样率16kHz，块大小512样本）
 * - 缓存时长：2秒（2000ms / 32ms = 62.5帧）
 * - 缓存用途：保存唤醒前的音频，用于服务器端语音识别
 *
 * @param data 音频数据指针，16位PCM格式
 * @param samples 音频数据样本数
 *
 * @note 该函数在检测线程中调用
 * @performance 使用循环缓冲区策略，控制内存使用
 */
void WakeWordDetect::StoreWakeWordData(uint16_t* data, size_t samples) {
    // === 存储音频数据到PCM缓冲区 ===
    // 使用emplace_back直接构造vector，避免额外的拷贝
    wake_word_pcm_.emplace_back(std::vector<int16_t>(data, data + samples));

    // === 维护缓冲区大小 ===
    /**
     * 保持约2秒的音频数据
     * 计算依据：
     * - 检测周期：32ms（采样率16kHz，块大小512样本）
     * - 目标时长：2000ms
     * - 最大帧数：2000 / 32 = 62.5帧
     */
    while (wake_word_pcm_.size() > 2000 / 32) {
        // 移除最旧的音频帧，保持缓冲区大小
        wake_word_pcm_.pop_front();
    }
}

/**
 * @brief 编码唤醒词音频数据
 *
 * 将缓存的唤醒前音频数据编码为OPUS格式，用于发送到服务器
 * 进行进一步的语音识别处理。编码过程在独立线程中进行。
 *
 * 编码配置：
 * - 采样率：16kHz
 * - 声道数：1（单声道）
 * - 编码复杂度：0（最快速度，适合实时处理）
 * - 帧时长：OPUS_FRAME_DURATION_MS
 *
 * 线程配置：
 * - 线程名称："encode_detect_packets"
 * - 堆栈大小：32KB（4096 * 8）
 * - 优先级：1（低优先级，后台处理）
 * - 内存分配：使用PSRAM
 *
 * @note 该函数会创建临时线程进行OPUS编码
 * @note 编码完成后可通过GetWakeWordOpus()获取结果
 * @performance 使用专用线程避免阻塞主流程
 */
void WakeWordDetect::EncodeWakeWordData() {
    // === 清空之前的编码结果 ===
    wake_word_opus_.clear();

    // === 分配编码任务堆栈内存 ===
    /**
     * 如果堆栈内存尚未分配，则从PSRAM中分配
     * 使用PSRAM可以节省宝贵的内部RAM资源
     */
    if (wake_word_encode_task_stack_ == nullptr) {
        wake_word_encode_task_stack_ = (StackType_t*)heap_caps_malloc(4096 * 8, MALLOC_CAP_SPIRAM);
    }

    // === 创建静态编码任务 ===
    /**
     * 使用xTaskCreateStatic创建静态任务，提供更好的内存管理
     * 任务配置：
     * - 使用lambda表达式作为任务函数
     * - 堆栈大小：32KB，足够处理OPUS编码
     * - 优先级：1，低优先级后台处理
     * - 使用预分配的PSRAM堆栈
     */
    wake_word_encode_task_ = xTaskCreateStatic([](void* arg) {
        // 将参数转换为WakeWordDetect指针
        auto this_ = (WakeWordDetect*)arg;
        {
            // === 性能计时开始 ===
            auto start_time = esp_timer_get_time();

            // === 创建OPUS编码器 ===
            /**
             * 配置OPUS编码器参数：
             * - 采样率：16kHz（语音处理标准采样率）
             * - 声道数：1（单声道）
             * - 帧时长：OPUS_FRAME_DURATION_MS
             */
            auto encoder = std::make_unique<OpusEncoderWrapper>(16000, 1, OPUS_FRAME_DURATION_MS);

            // 设置编码复杂度为0（最快速度），适合实时处理
            encoder->SetComplexity(0);

            // === 批量编码PCM音频数据 ===
            /**
             * 遍历所有缓存的PCM音频帧，逐一进行OPUS编码
             * 使用移动语义避免数据拷贝，提高性能
             */
            for (auto& pcm: this_->wake_word_pcm_) {
                encoder->Encode(std::move(pcm), [this_](std::vector<uint8_t>&& opus) {
                    // === 线程安全地存储编码结果 ===
                    std::lock_guard<std::mutex> lock(this_->wake_word_mutex_);

                    // 将编码后的OPUS数据添加到输出队列
                    this_->wake_word_opus_.emplace_back(std::move(opus));

                    // 通知等待线程有新数据可用
                    this_->wake_word_cv_.notify_all();
                });
            }

            // === 清空PCM缓冲区 ===
            // 编码完成后清空原始PCM数据，释放内存
            this_->wake_word_pcm_.clear();

            // === 性能计时结束 ===
            auto end_time = esp_timer_get_time();
            ESP_LOGI(TAG, "Encode wake word opus %zu packets in %lld ms",
                this_->wake_word_opus_.size(), (end_time - start_time) / 1000);

            // === 添加结束标记 ===
            /**
             * 添加空的OPUS数据包作为结束标记
             * 通知GetWakeWordOpus()函数编码已完成
             */
            std::lock_guard<std::mutex> lock(this_->wake_word_mutex_);
            this_->wake_word_opus_.push_back(std::vector<uint8_t>());
            this_->wake_word_cv_.notify_all();
        }

        // 任务完成，删除自身
        vTaskDelete(NULL);
    }, "encode_detect_packets", 4096 * 8, this, 1, wake_word_encode_task_stack_, &wake_word_encode_task_buffer_);
}

/**
 * @brief 获取编码后的唤醒词音频数据
 *
 * 获取OPUS编码后的唤醒词音频数据包。该函数会阻塞等待
 * 直到有可用的编码数据或编码完成。
 *
 * 工作机制：
 * 1. 使用互斥锁保护数据访问
 * 2. 使用条件变量等待数据可用
 * 3. 使用swap操作高效传递数据
 * 4. 通过返回值指示是否还有更多数据
 *
 * 返回值说明：
 * - true: 成功获取到音频数据包
 * - false: 编码已完成，无更多数据（遇到结束标记）
 *
 * @param opus 输出参数，接收OPUS编码的音频数据
 * @return true 成功获取到音频数据
 * @return false 编码已完成，无更多数据
 *
 * @note 该函数会阻塞等待，直到有数据可用
 * @thread_safety 线程安全，使用条件变量和互斥锁同步
 * @performance 使用swap操作避免数据拷贝
 */
bool WakeWordDetect::GetWakeWordOpus(std::vector<uint8_t>& opus) {
    // === 获取互斥锁，保护数据访问 ===
    std::unique_lock<std::mutex> lock(wake_word_mutex_);

    // === 等待数据可用 ===
    /**
     * 使用条件变量等待，直到OPUS队列非空
     * 这是一个阻塞操作，会一直等待直到：
     * 1. 编码线程产生新的OPUS数据包
     * 2. 编码线程添加结束标记（空数据包）
     */
    wake_word_cv_.wait(lock, [this]() {
        return !wake_word_opus_.empty();
    });

    // === 高效传递数据 ===
    /**
     * 使用swap操作将队列头部的数据与输出参数交换
     * 这种方式避免了数据拷贝，提高了性能
     */
    opus.swap(wake_word_opus_.front());

    // === 移除已处理的数据 ===
    wake_word_opus_.pop_front();

    // === 返回数据有效性 ===
    /**
     * 检查获取的数据是否为空
     * - 非空：有效的OPUS音频数据包
     * - 空：结束标记，表示编码已完成
     */
    return !opus.empty();
}
